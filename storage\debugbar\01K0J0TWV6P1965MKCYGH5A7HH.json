{"__meta": {"id": "01K0J0TWV6P1965MKCYGH5A7HH", "datetime": "2025-07-19 18:58:38", "utime": **********.090559, "method": "GET", "uri": "/en/projects/castle-view", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[18:58:30] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\laragon\\www\\xmetr\\storage\\framework\\views\\75b0c53f43692e18f16ab1977e310e0f.php on line 34", "message_html": null, "is_string": false, "label": "warning", "time": **********.016806, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752951489.385846, "end": **********.090591, "duration": 28.704745054244995, "duration_str": "28.7s", "measures": [{"label": "Booting", "start": 1752951489.385846, "relative_start": 0, "end": **********.066209, "relative_end": **********.066209, "duration": 2.***************, "duration_str": "2.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.066229, "relative_start": 2.****************, "end": **********.090595, "relative_end": 4.0531158447265625e-06, "duration": 26.**************, "duration_str": "26.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.391524, "relative_start": 4.***************, "end": **********.186677, "relative_end": **********.186677, "duration": 1.****************, "duration_str": "1.8s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.xmetr::views.real-estate.project", "start": **********.956424, "relative_start": 15.**************, "end": **********.956424, "relative_end": **********.956424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.social-sharing", "start": **********.659482, "relative_start": 17.**************, "end": **********.659482, "relative_end": **********.659482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0226480071dd0e74b211507465ab2265", "start": **********.928144, "relative_start": 17.542298078536987, "end": **********.928144, "relative_end": **********.928144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6112de9dcda50c4e5e8edc1897f5a562", "start": **********.931759, "relative_start": 17.545913219451904, "end": **********.931759, "relative_end": **********.931759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::72031399d6d0521c81f2b93c5dd13aa0", "start": **********.953305, "relative_start": 17.567459106445312, "end": **********.953305, "relative_end": **********.953305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13cc8f2acf7f8ddf927c806fc8761af", "start": **********.956048, "relative_start": 17.570202112197876, "end": **********.956048, "relative_end": **********.956048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.96667, "relative_start": 17.58082413673401, "end": **********.96667, "relative_end": **********.96667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.single-layouts.partials.reviews", "start": **********.986105, "relative_start": 17.60025906562805, "end": **********.986105, "relative_end": **********.986105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1752951508.239741, "relative_start": 18.85389518737793, "end": 1752951508.239741, "relative_end": 1752951508.239741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": 1752951508.400925, "relative_start": 19.015079021453857, "end": 1752951508.400925, "relative_end": 1752951508.400925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951508.456872, "relative_start": 19.07102608680725, "end": 1752951508.456872, "relative_end": 1752951508.456872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951508.630981, "relative_start": 19.245135068893433, "end": 1752951508.630981, "relative_end": 1752951508.630981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951508.850083, "relative_start": 19.464237213134766, "end": 1752951508.850083, "relative_end": 1752951508.850083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951508.904022, "relative_start": 19.518176078796387, "end": 1752951508.904022, "relative_end": 1752951508.904022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.002919, "relative_start": 19.61707305908203, "end": 1752951509.002919, "relative_end": 1752951509.002919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": 1752951509.039355, "relative_start": 19.65350914001465, "end": 1752951509.039355, "relative_end": 1752951509.039355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": 1752951509.209901, "relative_start": 19.824055194854736, "end": 1752951509.209901, "relative_end": 1752951509.209901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951509.21267, "relative_start": 19.826824188232422, "end": 1752951509.21267, "relative_end": 1752951509.21267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951509.213587, "relative_start": 19.827741146087646, "end": 1752951509.213587, "relative_end": 1752951509.213587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951509.214002, "relative_start": 19.828155994415283, "end": 1752951509.214002, "relative_end": 1752951509.214002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.214474, "relative_start": 19.828628063201904, "end": 1752951509.214474, "relative_end": 1752951509.214474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": 1752951509.215395, "relative_start": 19.829549074172974, "end": 1752951509.215395, "relative_end": 1752951509.215395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1752951509.218065, "relative_start": 19.832219123840332, "end": 1752951509.218065, "relative_end": 1752951509.218065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951509.256768, "relative_start": 19.870922088623047, "end": 1752951509.256768, "relative_end": 1752951509.256768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951509.257508, "relative_start": 19.871662139892578, "end": 1752951509.257508, "relative_end": 1752951509.257508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951509.257789, "relative_start": 19.87194299697876, "end": 1752951509.257789, "relative_end": 1752951509.257789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.258119, "relative_start": 19.872273206710815, "end": 1752951509.258119, "relative_end": 1752951509.258119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951509.258502, "relative_start": 19.872656106948853, "end": 1752951509.258502, "relative_end": 1752951509.258502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951509.258906, "relative_start": 19.87305998802185, "end": 1752951509.258906, "relative_end": 1752951509.258906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951509.259414, "relative_start": 19.873568058013916, "end": 1752951509.259414, "relative_end": 1752951509.259414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951509.259678, "relative_start": 19.87383198738098, "end": 1752951509.259678, "relative_end": 1752951509.259678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.259958, "relative_start": 19.874112129211426, "end": 1752951509.259958, "relative_end": 1752951509.259958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": 1752951509.260673, "relative_start": 19.87482714653015, "end": 1752951509.260673, "relative_end": 1752951509.260673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.343068, "relative_start": 19.95722198486328, "end": 1752951509.343068, "relative_end": 1752951509.343068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.label", "start": 1752951509.344647, "relative_start": 19.95880103111267, "end": 1752951509.344647, "relative_end": 1752951509.344647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951509.626902, "relative_start": 20.241056203842163, "end": 1752951509.626902, "relative_end": 1752951509.626902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": 1752951509.628454, "relative_start": 20.242608070373535, "end": 1752951509.628454, "relative_end": 1752951509.628454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951509.785255, "relative_start": 20.39940905570984, "end": 1752951509.785255, "relative_end": 1752951509.785255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951509.785807, "relative_start": 20.39996099472046, "end": 1752951509.785807, "relative_end": 1752951509.785807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.786378, "relative_start": 20.400532007217407, "end": 1752951509.786378, "relative_end": 1752951509.786378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951509.787003, "relative_start": 20.40115714073181, "end": 1752951509.787003, "relative_end": 1752951509.787003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951509.787663, "relative_start": 20.401817083358765, "end": 1752951509.787663, "relative_end": 1752951509.787663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951509.788473, "relative_start": 20.402626991271973, "end": 1752951509.788473, "relative_end": 1752951509.788473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951509.788856, "relative_start": 20.40301012992859, "end": 1752951509.788856, "relative_end": 1752951509.788856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.789307, "relative_start": 20.40346121788025, "end": 1752951509.789307, "relative_end": 1752951509.789307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": 1752951509.790187, "relative_start": 20.404340982437134, "end": 1752951509.790187, "relative_end": 1752951509.790187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951509.832088, "relative_start": 20.446242094039917, "end": 1752951509.832088, "relative_end": 1752951509.832088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1752951509.836668, "relative_start": 20.450822114944458, "end": 1752951509.836668, "relative_end": 1752951509.836668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.partials.star", "start": 1752951509.838498, "relative_start": 20.45265221595764, "end": 1752951509.838498, "relative_end": 1752951509.838498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": **********.396392, "relative_start": 21.01054620742798, "end": **********.396392, "relative_end": **********.396392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::views.real-estate.properties.item-grid", "start": 1752951511.291589, "relative_start": 21.90574312210083, "end": 1752951511.291589, "relative_end": 1752951511.291589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd248739db8ba923ebfe1f426bb71a38", "start": 1752951511.512662, "relative_start": 22.126816034317017, "end": 1752951511.512662, "relative_end": 1752951511.512662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.full-width", "start": 1752951511.519329, "relative_start": 22.133483171463013, "end": 1752951511.519329, "relative_end": 1752951511.519329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.023845, "relative_start": 22.637999057769775, "end": **********.023845, "relative_end": **********.023845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.header", "start": **********.184512, "relative_start": 22.79866600036621, "end": **********.184512, "relative_end": **********.184512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1752951513.264669, "relative_start": 23.878823041915894, "end": 1752951513.264669, "relative_end": 1752951513.264669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1752951513.621365, "relative_start": 24.23551917076111, "end": 1752951513.621365, "relative_end": 1752951513.621365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": 1752951513.715464, "relative_start": 24.329618215560913, "end": 1752951513.715464, "relative_end": 1752951513.715464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": 1752951513.716743, "relative_start": 24.330897092819214, "end": 1752951513.716743, "relative_end": 1752951513.716743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": 1752951513.896341, "relative_start": 24.51049518585205, "end": 1752951513.896341, "relative_end": 1752951513.896341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1752951514.036529, "relative_start": 24.650683164596558, "end": 1752951514.036529, "relative_end": 1752951514.036529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": 1752951514.279881, "relative_start": 24.89403510093689, "end": 1752951514.279881, "relative_end": 1752951514.279881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1752951514.324476, "relative_start": 24.93863010406494, "end": 1752951514.324476, "relative_end": 1752951514.324476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": 1752951514.397531, "relative_start": 25.011685132980347, "end": 1752951514.397531, "relative_end": 1752951514.397531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "start": 1752951514.445584, "relative_start": 25.059738159179688, "end": 1752951514.445584, "relative_end": 1752951514.445584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": 1752951514.446574, "relative_start": 25.060728073120117, "end": 1752951514.446574, "relative_end": 1752951514.446574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a7781df99689693edd2c6c189d767089", "start": 1752951514.465457, "relative_start": 25.07961106300354, "end": 1752951514.465457, "relative_end": 1752951514.465457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1752951514.527516, "relative_start": 25.141669988632202, "end": 1752951514.527516, "relative_end": 1752951514.527516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::054b349d8fa17e9f8644145c2fbc1063", "start": 1752951514.52843, "relative_start": 25.142584085464478, "end": 1752951514.52843, "relative_end": 1752951514.52843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6cb431528960518c12f7bad152d7b46d", "start": 1752951514.529944, "relative_start": 25.144098043441772, "end": 1752951514.529944, "relative_end": 1752951514.529944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/social-login::login-options", "start": 1752951514.531736, "relative_start": 25.1458899974823, "end": 1752951514.531736, "relative_end": 1752951514.531736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.modal-authentication", "start": 1752951514.536193, "relative_start": 25.150346994400024, "end": 1752951514.536193, "relative_end": 1752951514.536193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1752951514.81096, "relative_start": 25.425114154815674, "end": 1752951514.81096, "relative_end": 1752951514.81096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": 1752951514.813149, "relative_start": 25.427303075790405, "end": 1752951514.813149, "relative_end": 1752951514.813149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951514.81383, "relative_start": 25.42798399925232, "end": 1752951514.81383, "relative_end": 1752951514.81383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951514.814391, "relative_start": 25.428544998168945, "end": 1752951514.814391, "relative_end": 1752951514.814391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951514.815044, "relative_start": 25.429198026657104, "end": 1752951514.815044, "relative_end": 1752951514.815044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951514.815396, "relative_start": 25.429550170898438, "end": 1752951514.815396, "relative_end": 1752951514.815396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951514.81575, "relative_start": 25.429903984069824, "end": 1752951514.81575, "relative_end": 1752951514.81575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": 1752951514.816557, "relative_start": 25.430711030960083, "end": 1752951514.816557, "relative_end": 1752951514.816557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1752951515.098998, "relative_start": 25.713152170181274, "end": 1752951515.098998, "relative_end": 1752951515.098998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951515.099487, "relative_start": 25.71364116668701, "end": 1752951515.099487, "relative_end": 1752951515.099487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951515.100264, "relative_start": 25.714418172836304, "end": 1752951515.100264, "relative_end": 1752951515.100264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951515.100641, "relative_start": 25.714795112609863, "end": 1752951515.100641, "relative_end": 1752951515.100641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951515.101135, "relative_start": 25.71528911590576, "end": 1752951515.101135, "relative_end": 1752951515.101135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": 1752951515.101981, "relative_start": 25.716135025024414, "end": 1752951515.101981, "relative_end": 1752951515.101981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1752951515.362558, "relative_start": 25.976711988449097, "end": 1752951515.362558, "relative_end": 1752951515.362558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951515.36327, "relative_start": 25.977424144744873, "end": 1752951515.36327, "relative_end": 1752951515.36327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951515.364147, "relative_start": 25.97830104827881, "end": 1752951515.364147, "relative_end": 1752951515.364147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951515.364522, "relative_start": 25.978676080703735, "end": 1752951515.364522, "relative_end": 1752951515.364522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951515.365092, "relative_start": 25.979246139526367, "end": 1752951515.365092, "relative_end": 1752951515.365092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951515.365756, "relative_start": 25.979910135269165, "end": 1752951515.365756, "relative_end": 1752951515.365756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951515.366361, "relative_start": 25.980515003204346, "end": 1752951515.366361, "relative_end": 1752951515.366361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951515.367231, "relative_start": 25.981384992599487, "end": 1752951515.367231, "relative_end": 1752951515.367231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951515.367631, "relative_start": 25.98178505897522, "end": 1752951515.367631, "relative_end": 1752951515.367631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951515.368132, "relative_start": 25.98228621482849, "end": 1752951515.368132, "relative_end": 1752951515.368132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off-checkbox", "start": 1752951515.369376, "relative_start": 25.983530044555664, "end": 1752951515.369376, "relative_end": 1752951515.369376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off-checkbox", "start": 1752951515.64633, "relative_start": 26.260484218597412, "end": 1752951515.64633, "relative_end": 1752951515.64633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.on-off.checkbox", "start": 1752951515.877602, "relative_start": 26.491756200790405, "end": 1752951515.877602, "relative_end": 1752951515.877602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": 1752951516.138817, "relative_start": 26.752971172332764, "end": 1752951516.138817, "relative_end": 1752951516.138817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.269564, "relative_start": 26.883718013763428, "end": 1752951516.269564, "relative_end": 1752951516.269564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.270375, "relative_start": 26.88452911376953, "end": 1752951516.270375, "relative_end": 1752951516.270375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.270624, "relative_start": 26.884778022766113, "end": 1752951516.270624, "relative_end": 1752951516.270624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.270928, "relative_start": 26.885082006454468, "end": 1752951516.270928, "relative_end": 1752951516.270928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.271353, "relative_start": 26.885507106781006, "end": 1752951516.271353, "relative_end": 1752951516.271353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.271828, "relative_start": 26.885982036590576, "end": 1752951516.271828, "relative_end": 1752951516.271828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.272379, "relative_start": 26.88653302192688, "end": 1752951516.272379, "relative_end": 1752951516.272379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.272631, "relative_start": 26.88678503036499, "end": 1752951516.272631, "relative_end": 1752951516.272631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.272961, "relative_start": 26.887115001678467, "end": 1752951516.272961, "relative_end": 1752951516.272961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.273301, "relative_start": 26.887454986572266, "end": 1752951516.273301, "relative_end": 1752951516.273301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.273644, "relative_start": 26.887798070907593, "end": 1752951516.273644, "relative_end": 1752951516.273644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.274094, "relative_start": 26.888248205184937, "end": 1752951516.274094, "relative_end": 1752951516.274094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.274314, "relative_start": 26.888468027114868, "end": 1752951516.274314, "relative_end": 1752951516.274314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.274585, "relative_start": 26.888739109039307, "end": 1752951516.274585, "relative_end": 1752951516.274585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.274907, "relative_start": 26.889061212539673, "end": 1752951516.274907, "relative_end": 1752951516.274907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.275235, "relative_start": 26.889389038085938, "end": 1752951516.275235, "relative_end": 1752951516.275235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.275687, "relative_start": 26.889841079711914, "end": 1752951516.275687, "relative_end": 1752951516.275687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.275906, "relative_start": 26.89006018638611, "end": 1752951516.275906, "relative_end": 1752951516.275906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.276184, "relative_start": 26.89033818244934, "end": 1752951516.276184, "relative_end": 1752951516.276184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.276486, "relative_start": 26.890640020370483, "end": 1752951516.276486, "relative_end": 1752951516.276486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.276814, "relative_start": 26.890968084335327, "end": 1752951516.276814, "relative_end": 1752951516.276814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.277247, "relative_start": 26.891401052474976, "end": 1752951516.277247, "relative_end": 1752951516.277247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.277468, "relative_start": 26.891622066497803, "end": 1752951516.277468, "relative_end": 1752951516.277468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.27772, "relative_start": 26.891874074935913, "end": 1752951516.27772, "relative_end": 1752951516.27772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": 1752951516.278024, "relative_start": 26.892178058624268, "end": 1752951516.278024, "relative_end": 1752951516.278024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.278438, "relative_start": 26.892592191696167, "end": 1752951516.278438, "relative_end": 1752951516.278438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.278814, "relative_start": 26.89296817779541, "end": 1752951516.278814, "relative_end": 1752951516.278814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.279224, "relative_start": 26.893378019332886, "end": 1752951516.279224, "relative_end": 1752951516.279224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.279695, "relative_start": 26.89384913444519, "end": 1752951516.279695, "relative_end": 1752951516.279695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.279925, "relative_start": 26.894079208374023, "end": 1752951516.279925, "relative_end": 1752951516.279925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.280208, "relative_start": 26.894362211227417, "end": 1752951516.280208, "relative_end": 1752951516.280208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.280519, "relative_start": 26.894673109054565, "end": 1752951516.280519, "relative_end": 1752951516.280519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.280848, "relative_start": 26.895002126693726, "end": 1752951516.280848, "relative_end": 1752951516.280848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.281294, "relative_start": 26.895448207855225, "end": 1752951516.281294, "relative_end": 1752951516.281294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.281515, "relative_start": 26.895668983459473, "end": 1752951516.281515, "relative_end": 1752951516.281515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.281788, "relative_start": 26.895942211151123, "end": 1752951516.281788, "relative_end": 1752951516.281788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.282094, "relative_start": 26.89624810218811, "end": 1752951516.282094, "relative_end": 1752951516.282094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.282417, "relative_start": 26.896571159362793, "end": 1752951516.282417, "relative_end": 1752951516.282417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.282867, "relative_start": 26.897021055221558, "end": 1752951516.282867, "relative_end": 1752951516.282867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.283087, "relative_start": 26.89724111557007, "end": 1752951516.283087, "relative_end": 1752951516.283087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.283373, "relative_start": 26.89752721786499, "end": 1752951516.283373, "relative_end": 1752951516.283373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.283706, "relative_start": 26.897860050201416, "end": 1752951516.283706, "relative_end": 1752951516.283706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.284071, "relative_start": 26.89822506904602, "end": 1752951516.284071, "relative_end": 1752951516.284071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.284567, "relative_start": 26.89872121810913, "end": 1752951516.284567, "relative_end": 1752951516.284567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.284812, "relative_start": 26.898966073989868, "end": 1752951516.284812, "relative_end": 1752951516.284812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.285081, "relative_start": 26.899235010147095, "end": 1752951516.285081, "relative_end": 1752951516.285081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1752951516.286988, "relative_start": 26.901142120361328, "end": 1752951516.286988, "relative_end": 1752951516.286988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1752951516.36383, "relative_start": 26.977984189987183, "end": 1752951516.36383, "relative_end": 1752951516.36383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::forms.auth", "start": 1752951516.365485, "relative_start": 26.979639053344727, "end": 1752951516.365485, "relative_end": 1752951516.365485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "start": 1752951516.752921, "relative_start": 27.367075204849243, "end": 1752951516.752921, "relative_end": 1752951516.752921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": 1752951516.790171, "relative_start": 27.404325008392334, "end": 1752951516.790171, "relative_end": 1752951516.790171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.793229, "relative_start": 27.40738320350647, "end": 1752951516.793229, "relative_end": 1752951516.793229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.793989, "relative_start": 27.408143043518066, "end": 1752951516.793989, "relative_end": 1752951516.793989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.795448, "relative_start": 27.409602165222168, "end": 1752951516.795448, "relative_end": 1752951516.795448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.795895, "relative_start": 27.410049200057983, "end": 1752951516.795895, "relative_end": 1752951516.795895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.7963, "relative_start": 27.410454034805298, "end": 1752951516.7963, "relative_end": 1752951516.7963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": 1752951516.79698, "relative_start": 27.411134004592896, "end": 1752951516.79698, "relative_end": 1752951516.79698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1752951516.954583, "relative_start": 27.568737030029297, "end": 1752951516.954583, "relative_end": 1752951516.954583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.955155, "relative_start": 27.56930899620056, "end": 1752951516.955155, "relative_end": 1752951516.955155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.955985, "relative_start": 27.570139169692993, "end": 1752951516.955985, "relative_end": 1752951516.955985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.956353, "relative_start": 27.570507049560547, "end": 1752951516.956353, "relative_end": 1752951516.956353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.956809, "relative_start": 27.570963144302368, "end": 1752951516.956809, "relative_end": 1752951516.956809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": 1752951516.957372, "relative_start": 27.571526050567627, "end": 1752951516.957372, "relative_end": 1752951516.957372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1752951516.958018, "relative_start": 27.572172164916992, "end": 1752951516.958018, "relative_end": 1752951516.958018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.958481, "relative_start": 27.572635173797607, "end": 1752951516.958481, "relative_end": 1752951516.958481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.959262, "relative_start": 27.573415994644165, "end": 1752951516.959262, "relative_end": 1752951516.959262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.95962, "relative_start": 27.573774099349976, "end": 1752951516.95962, "relative_end": 1752951516.95962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.960076, "relative_start": 27.574230194091797, "end": 1752951516.960076, "relative_end": 1752951516.960076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.password", "start": 1752951516.960634, "relative_start": 27.574788093566895, "end": 1752951516.960634, "relative_end": 1752951516.960634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": 1752951516.961276, "relative_start": 27.575430154800415, "end": 1752951516.961276, "relative_end": 1752951516.961276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.961699, "relative_start": 27.57585310935974, "end": 1752951516.961699, "relative_end": 1752951516.961699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.962483, "relative_start": 27.576637029647827, "end": 1752951516.962483, "relative_end": 1752951516.962483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.962852, "relative_start": 27.577006101608276, "end": 1752951516.962852, "relative_end": 1752951516.962852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.963299, "relative_start": 27.577453136444092, "end": 1752951516.963299, "relative_end": 1752951516.963299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.963819, "relative_start": 27.577973127365112, "end": 1752951516.963819, "relative_end": 1752951516.963819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.964361, "relative_start": 27.57851505279541, "end": 1752951516.964361, "relative_end": 1752951516.964361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.965109, "relative_start": 27.57926321029663, "end": 1752951516.965109, "relative_end": 1752951516.965109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.96546, "relative_start": 27.57961416244507, "end": 1752951516.96546, "relative_end": 1752951516.96546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.965865, "relative_start": 27.580018997192383, "end": 1752951516.965865, "relative_end": 1752951516.965865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.966347, "relative_start": 27.580501079559326, "end": 1752951516.966347, "relative_end": 1752951516.966347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.966878, "relative_start": 27.581032037734985, "end": 1752951516.966878, "relative_end": 1752951516.966878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.967623, "relative_start": 27.581777095794678, "end": 1752951516.967623, "relative_end": 1752951516.967623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.967957, "relative_start": 27.582111120224, "end": 1752951516.967957, "relative_end": 1752951516.967957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.968319, "relative_start": 27.582473039627075, "end": 1752951516.968319, "relative_end": 1752951516.968319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": 1752951516.968788, "relative_start": 27.582942008972168, "end": 1752951516.968788, "relative_end": 1752951516.968788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.969385, "relative_start": 27.58353900909424, "end": 1752951516.969385, "relative_end": 1752951516.969385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.9703, "relative_start": 27.58445405960083, "end": 1752951516.9703, "relative_end": 1752951516.9703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.970744, "relative_start": 27.584897994995117, "end": 1752951516.970744, "relative_end": 1752951516.970744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.97124, "relative_start": 27.585394144058228, "end": 1752951516.97124, "relative_end": 1752951516.97124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.971474, "relative_start": 27.585628032684326, "end": 1752951516.971474, "relative_end": 1752951516.971474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.971763, "relative_start": 27.585916996002197, "end": 1752951516.971763, "relative_end": 1752951516.971763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.972085, "relative_start": 27.586239099502563, "end": 1752951516.972085, "relative_end": 1752951516.972085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.972437, "relative_start": 27.586591005325317, "end": 1752951516.972437, "relative_end": 1752951516.972437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.972897, "relative_start": 27.587051153182983, "end": 1752951516.972897, "relative_end": 1752951516.972897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.97312, "relative_start": 27.587274074554443, "end": 1752951516.97312, "relative_end": 1752951516.97312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.973401, "relative_start": 27.587555170059204, "end": 1752951516.973401, "relative_end": 1752951516.973401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.973749, "relative_start": 27.587903022766113, "end": 1752951516.973749, "relative_end": 1752951516.973749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.974093, "relative_start": 27.588247060775757, "end": 1752951516.974093, "relative_end": 1752951516.974093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.974548, "relative_start": 27.58870220184326, "end": 1752951516.974548, "relative_end": 1752951516.974548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.974772, "relative_start": 27.588926076889038, "end": 1752951516.974772, "relative_end": 1752951516.974772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.975066, "relative_start": 27.58922004699707, "end": 1752951516.975066, "relative_end": 1752951516.975066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": 1752951516.975391, "relative_start": 27.589545011520386, "end": 1752951516.975391, "relative_end": 1752951516.975391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": 1752951516.975738, "relative_start": 27.589892148971558, "end": 1752951516.975738, "relative_end": 1752951516.975738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": 1752951516.976198, "relative_start": 27.590352058410645, "end": 1752951516.976198, "relative_end": 1752951516.976198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": 1752951516.976423, "relative_start": 27.590577125549316, "end": 1752951516.976423, "relative_end": 1752951516.976423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": 1752951516.97668, "relative_start": 27.590834140777588, "end": 1752951516.97668, "relative_end": 1752951516.97668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": 1752951516.979475, "relative_start": 27.593629121780396, "end": 1752951516.979475, "relative_end": 1752951516.979475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::partials.footer", "start": 1752951516.981386, "relative_start": 27.595540046691895, "end": 1752951516.981386, "relative_end": 1752951516.981386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ads::partials.ad-display", "start": **********.034156, "relative_start": 27.64831018447876, "end": **********.034156, "relative_end": **********.034156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.xmetr::layouts.base", "start": **********.035526, "relative_start": 27.649680137634277, "end": **********.035526, "relative_end": **********.035526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.213797, "relative_start": 27.827951192855835, "end": **********.213797, "relative_end": **********.213797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.hreflang", "start": **********.416418, "relative_start": 28.030572175979614, "end": **********.416418, "relative_end": **********.416418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.479747, "relative_start": 28.09390115737915, "end": **********.479747, "relative_end": **********.479747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.597655, "relative_start": 28.211809158325195, "end": **********.597655, "relative_end": **********.597655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.852429, "relative_start": 28.466583013534546, "end": **********.91621, "relative_end": **********.91621, "duration": 0.0637810230255127, "duration_str": "63.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 67089152, "peak_usage_str": "64MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 214, "nb_templates": 214, "templates": [{"name": "1x theme.xmetr::views.real-estate.project", "param_count": null, "params": [], "start": **********.95639, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.phptheme.xmetr::views.real-estate.project", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproject.blade.php:1", "ajax": false, "filename": "project.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.project"}, {"name": "1x theme.xmetr::views.real-estate.partials.social-sharing", "param_count": null, "params": [], "start": **********.65942, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/social-sharing.blade.phptheme.xmetr::views.real-estate.partials.social-sharing", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Fsocial-sharing.blade.php:1", "ajax": false, "filename": "social-sharing.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.social-sharing"}, {"name": "1x __components::0226480071dd0e74b211507465ab2265", "param_count": null, "params": [], "start": **********.928105, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/0226480071dd0e74b211507465ab2265.blade.php__components::0226480071dd0e74b211507465ab2265", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F0226480071dd0e74b211507465ab2265.blade.php:1", "ajax": false, "filename": "0226480071dd0e74b211507465ab2265.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0226480071dd0e74b211507465ab2265"}, {"name": "1x __components::6112de9dcda50c4e5e8edc1897f5a562", "param_count": null, "params": [], "start": **********.931734, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6112de9dcda50c4e5e8edc1897f5a562.blade.php__components::6112de9dcda50c4e5e8edc1897f5a562", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6112de9dcda50c4e5e8edc1897f5a562.blade.php:1", "ajax": false, "filename": "6112de9dcda50c4e5e8edc1897f5a562.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6112de9dcda50c4e5e8edc1897f5a562"}, {"name": "1x __components::72031399d6d0521c81f2b93c5dd13aa0", "param_count": null, "params": [], "start": **********.95328, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/72031399d6d0521c81f2b93c5dd13aa0.blade.php__components::72031399d6d0521c81f2b93c5dd13aa0", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F72031399d6d0521c81f2b93c5dd13aa0.blade.php:1", "ajax": false, "filename": "72031399d6d0521c81f2b93c5dd13aa0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::72031399d6d0521c81f2b93c5dd13aa0"}, {"name": "1x __components::b13cc8f2acf7f8ddf927c806fc8761af", "param_count": null, "params": [], "start": **********.956022, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/b13cc8f2acf7f8ddf927c806fc8761af.blade.php__components::b13cc8f2acf7f8ddf927c806fc8761af", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fb13cc8f2acf7f8ddf927c806fc8761af.blade.php:1", "ajax": false, "filename": "b13cc8f2acf7f8ddf927c806fc8761af.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b13cc8f2acf7f8ddf927c806fc8761af"}, {"name": "3x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.966644, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php:1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "1x theme.xmetr::views.real-estate.single-layouts.partials.reviews", "param_count": null, "params": [], "start": **********.98608, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/single-layouts/partials/reviews.blade.phptheme.xmetr::views.real-estate.single-layouts.partials.reviews", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fsingle-layouts%2Fpartials%2Freviews.blade.php:1", "ajax": false, "filename": "reviews.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.single-layouts.partials.reviews"}, {"name": "6x core/js-validation::bootstrap", "param_count": null, "params": [], "start": 1752951508.239717, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php:1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/js-validation::bootstrap"}, {"name": "2x core/base::forms.form-content-only", "param_count": null, "params": [], "start": 1752951508.400899, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php:1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.form-content-only"}, {"name": "20x core/base::forms.fields.html", "param_count": null, "params": [], "start": 1752951508.456848, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php:1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::forms.fields.html"}, {"name": "29x ********************************::form.field", "param_count": null, "params": [], "start": 1752951508.630934, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php:1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 29, "name_original": "********************************::form.field"}, {"name": "29x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": 1752951508.850058, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php:1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 29, "name_original": "core/base::forms.partials.help-block"}, {"name": "29x core/base::forms.partials.errors", "param_count": null, "params": [], "start": 1752951508.903997, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php:1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 29, "name_original": "core/base::forms.partials.errors"}, {"name": "33x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": 1752951509.002894, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php:1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 33, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": 1752951509.039329, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php:1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.custom-select"}, {"name": "1x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": 1752951509.209856, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php:1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.custom-select"}, {"name": "1x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": 1752951509.215371, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php:1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.textarea"}, {"name": "6x core/base::forms.partials.label", "param_count": null, "params": [], "start": 1752951509.21803, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/base::forms.partials.label"}, {"name": "3x laravel-form-builder::button", "param_count": null, "params": [], "start": 1752951509.260641, "type": "php", "hash": "phpD:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php:1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 3, "name_original": "laravel-form-builder::button"}, {"name": "1x core/base::forms.fields.label", "param_count": null, "params": [], "start": 1752951509.344588, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/label.blade.phpcore/base::forms.fields.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.label"}, {"name": "1x ********************************::form.label", "param_count": null, "params": [], "start": 1752951509.628426, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/label.blade.php********************************::form.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.label"}, {"name": "1x laravel-form-builder::text", "param_count": null, "params": [], "start": 1752951509.790151, "type": "php", "hash": "phpD:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src/../resources/views/text.phplaravel-form-builder::text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Ftext.php:1", "ajax": false, "filename": "text.php", "line": "?"}, "render_count": 1, "name_original": "laravel-form-builder::text"}, {"name": "1x theme.xmetr::views.real-estate.partials.star", "param_count": null, "params": [], "start": 1752951509.838471, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/partials/star.blade.phptheme.xmetr::views.real-estate.partials.star", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fpartials%2Fstar.blade.php:1", "ajax": false, "filename": "star.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::views.real-estate.partials.star"}, {"name": "2x theme.xmetr::views.real-estate.properties.item-grid", "param_count": null, "params": [], "start": **********.396339, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.phptheme.xmetr::views.real-estate.properties.item-grid", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fviews%2Freal-estate%2Fproperties%2Fitem-grid.blade.php:1", "ajax": false, "filename": "item-grid.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.xmetr::views.real-estate.properties.item-grid"}, {"name": "1x __components::dd248739db8ba923ebfe1f426bb71a38", "param_count": null, "params": [], "start": 1752951511.512604, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd248739db8ba923ebfe1f426bb71a38.blade.php__components::dd248739db8ba923ebfe1f426bb71a38", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd248739db8ba923ebfe1f426bb71a38.blade.php:1", "ajax": false, "filename": "dd248739db8ba923ebfe1f426bb71a38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd248739db8ba923ebfe1f426bb71a38"}, {"name": "1x theme.xmetr::layouts.full-width", "param_count": null, "params": [], "start": 1752951511.519271, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/full-width.blade.phptheme.xmetr::layouts.full-width", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Ffull-width.blade.php:1", "ajax": false, "filename": "full-width.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.full-width"}, {"name": "4x plugins/ads::partials.ad-display", "param_count": null, "params": [], "start": **********.0238, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/ads/resources/views/partials/ad-display.blade.phpplugins/ads::partials.ad-display", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fads%2Fresources%2Fviews%2Fpartials%2Fad-display.blade.php:1", "ajax": false, "filename": "ad-display.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/ads::partials.ad-display"}, {"name": "1x theme.xmetr::partials.header", "param_count": null, "params": [], "start": **********.184466, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/header.blade.phptheme.xmetr::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.header"}, {"name": "4x plugins/social-login::login-options", "param_count": null, "params": [], "start": 1752951513.264619, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/social-login/resources/views/login-options.blade.phpplugins/social-login::login-options", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Fsocial-login%2Fresources%2Fviews%2Flogin-options.blade.php:1", "ajax": false, "filename": "login-options.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/social-login::login-options"}, {"name": "3x __components::054b349d8fa17e9f8644145c2fbc1063", "param_count": null, "params": [], "start": 1752951514.036477, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/054b349d8fa17e9f8644145c2fbc1063.blade.php__components::054b349d8fa17e9f8644145c2fbc1063", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F054b349d8fa17e9f8644145c2fbc1063.blade.php:1", "ajax": false, "filename": "054b349d8fa17e9f8644145c2fbc1063.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::054b349d8fa17e9f8644145c2fbc1063"}, {"name": "2x __components::6cb431528960518c12f7bad152d7b46d", "param_count": null, "params": [], "start": 1752951514.279819, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6cb431528960518c12f7bad152d7b46d.blade.php__components::6cb431528960518c12f7bad152d7b46d", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6cb431528960518c12f7bad152d7b46d.blade.php:1", "ajax": false, "filename": "6cb431528960518c12f7bad152d7b46d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6cb431528960518c12f7bad152d7b46d"}, {"name": "2x __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "param_count": null, "params": [], "start": 1752951514.397477, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php__components::f6b8d5c7f3ac3703ebfdef4449133ff4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff6b8d5c7f3ac3703ebfdef4449133ff4.blade.php:1", "ajax": false, "filename": "f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f6b8d5c7f3ac3703ebfdef4449133ff4"}, {"name": "1x __components::a7781df99689693edd2c6c189d767089", "param_count": null, "params": [], "start": 1752951514.46541, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a7781df99689693edd2c6c189d767089.blade.php__components::a7781df99689693edd2c6c189d767089", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa7781df99689693edd2c6c189d767089.blade.php:1", "ajax": false, "filename": "a7781df99689693edd2c6c189d767089.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a7781df99689693edd2c6c189d767089"}, {"name": "1x theme.xmetr::partials.modal-authentication", "param_count": null, "params": [], "start": 1752951514.536145, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/modal-authentication.blade.phptheme.xmetr::partials.modal-authentication", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Fmodal-authentication.blade.php:1", "ajax": false, "filename": "modal-authentication.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.modal-authentication"}, {"name": "2x core/base::forms.fields.email", "param_count": null, "params": [], "start": 1752951514.816521, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php:1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.email"}, {"name": "2x core/base::forms.fields.password", "param_count": null, "params": [], "start": 1752951515.101947, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/password.blade.phpcore/base::forms.fields.password", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fpassword.blade.php:1", "ajax": false, "filename": "password.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.password"}, {"name": "1x core/base::forms.fields.on-off-checkbox", "param_count": null, "params": [], "start": 1752951515.369332, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off-checkbox.blade.phpcore/base::forms.fields.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off-checkbox"}, {"name": "1x core/base::forms.partials.on-off-checkbox", "param_count": null, "params": [], "start": 1752951515.646278, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off-checkbox.blade.phpcore/base::forms.partials.on-off-checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off-checkbox.blade.php:1", "ajax": false, "filename": "on-off-checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off-checkbox"}, {"name": "1x ********************************::form.on-off.checkbox", "param_count": null, "params": [], "start": 1752951515.877568, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php********************************::form.on-off.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": 1752951516.138753, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "1x plugins/real-estate::forms.auth", "param_count": null, "params": [], "start": 1752951516.365462, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/forms/auth.blade.phpplugins/real-estate::forms.auth", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fforms%2Fauth.blade.php:1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/real-estate::forms.auth"}, {"name": "1x __components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "param_count": null, "params": [], "start": 1752951516.752869, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php__components::2844accb98b3a6a2e1cb0b4f06fc3e6e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php:1", "ajax": false, "filename": "2844accb98b3a6a2e1cb0b4f06fc3e6e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2844accb98b3a6a2e1cb0b4f06fc3e6e"}, {"name": "1x core/base::forms.columns.form-open-wrapper", "param_count": null, "params": [], "start": 1752951516.790115, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/form-open-wrapper.blade.phpcore/base::forms.columns.form-open-wrapper", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fform-open-wrapper.blade.php:1", "ajax": false, "filename": "form-open-wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.columns.form-open-wrapper"}, {"name": "1x core/base::forms.fields.text", "param_count": null, "params": [], "start": 1752951516.796955, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php:1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.text"}, {"name": "1x theme.xmetr::partials.footer", "param_count": null, "params": [], "start": 1752951516.981361, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/partials/footer.blade.phptheme.xmetr::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::partials.footer"}, {"name": "1x theme.xmetr::layouts.base", "param_count": null, "params": [], "start": **********.035481, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\themes/xmetr/layouts/base.blade.phptheme.xmetr::layouts.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Flayouts%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.xmetr::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.213764, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "1x plugins/language::partials.hreflang", "param_count": null, "params": [], "start": **********.41639, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/hreflang.blade.phpplugins/language::partials.hreflang", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fhreflang.blade.php:1", "ajax": false, "filename": "hreflang.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language::partials.hreflang"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.479722, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.597613, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php:1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 47, "nb_statements": 47, "nb_visible_statements": 47, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.21825, "accumulated_duration_str": "218ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs_translations` where `lang_code` = 'en' and `key` = 'castle-view' limit 1", "type": "query", "params": [], "bindings": ["en", "castle-view"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.231066, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:98", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 98}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:98", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "98"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.279}, {"sql": "select * from `slugs` where `key` = 'castle-view' limit 1", "type": "query", "params": [], "bindings": ["castle-view"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, {"index": 15, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2349792, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "RedirectIncorrectLanguagePrefix.php:107", "source": {"index": 14, "namespace": null, "name": "app/Http/Middleware/RedirectIncorrectLanguagePrefix.php", "file": "D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php", "line": 107}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fapp%2FHttp%2FMiddleware%2FRedirectIncorrectLanguagePrefix.php:107", "ajax": false, "filename": "RedirectIncorrectLanguagePrefix.php", "line": "107"}, "connection": "xmetr", "explain": null, "start_percent": 0.279, "width_percent": 0.211}, {"sql": "select * from `slugs` where (`key` = 'castle-view' and `prefix` = 'projects') or exists (select * from `slugs_translations` where `slugs`.`id` = `slugs_translations`.`slugs_id` and (`key` = 'castle-view' and `prefix` = 'projects')) limit 1", "type": "query", "params": [], "bindings": ["castle-view", "projects", "castle-view", "projects"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.387793, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.49, "width_percent": 0.211}, {"sql": "select * from `re_projects` where (`id` = 25 and `re_projects`.`status` != 'not_available') limit 1", "type": "query", "params": [], "bindings": [25, "not_available"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 155}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}], "start": **********.451097, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.701, "width_percent": 0.243}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project' and `slugs`.`reference_id` = 25 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Project", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 157}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.588519, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.944, "width_percent": 0.225}, {"sql": "select * from `re_projects` where `id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/ProjectRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\ProjectRepository.php", "line": 305}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 181}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}], "start": **********.5563228, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.168, "width_percent": 0.513}, {"sql": "select `re_categories`.*, `re_project_categories`.`project_id` as `pivot_project_id`, `re_project_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_project_categories` on `re_categories`.`id` = `re_project_categories`.`category_id` where `re_project_categories`.`project_id` in (25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 52}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/ProjectRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\ProjectRepository.php", "line": 305}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 181}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}], "start": **********.646814, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.682, "width_percent": 0.302}, {"sql": "select * from `re_projects` where (`re_projects`.`status` != 'not_available') and not `id` = 25 order by `created_at` desc limit 3", "type": "query", "params": [], "bindings": ["not_available", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/ProjectRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\ProjectRepository.php", "line": 328}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Services/HandleFrontPages.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\HandleFrontPages.php", "line": 181}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}], "start": **********.650889, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.984, "width_percent": 0.422}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (25) and `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/packages/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 798}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.753854, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.405, "width_percent": 0.252}, {"sql": "select * from `cities` where `cities`.`id` = 8175 limit 1", "type": "query", "params": [], "bindings": [8175], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0895848, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.658, "width_percent": 0.426}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\City' and `slugs`.`reference_id` = 8175 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\City", 8175], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 28}], "start": **********.0953119, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 3.084, "width_percent": 0.522}, {"sql": "select * from `re_property_daily_views` where `property_id` = 25 and `view_date` = '2025-07-19' limit 1", "type": "query", "params": [], "bindings": [25, "2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Models/PropertyDailyView.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\PropertyDailyView.php", "line": 58}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Project.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Project.php", "line": 274}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Project.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Project.php", "line": 284}, {"index": 26, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.501076, "duration": 0.01316, "duration_str": "13.16ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 3.606, "width_percent": 6.03}, {"sql": "select `re_features`.*, `re_project_features`.`project_id` as `pivot_project_id`, `re_project_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_project_features` on `re_features`.`id` = `re_project_features`.`feature_id` where `re_project_features`.`project_id` = 25", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 217}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.587119, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 9.636, "width_percent": 0.444}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 167 limit 1", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 256}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0712621, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 10.08, "width_percent": 0.628}, {"sql": "select `re_spoken_languages`.*, `re_account_spoken_languages`.`account_id` as `pivot_account_id`, `re_account_spoken_languages`.`spoken_language_id` as `pivot_spoken_language_id` from `re_spoken_languages` inner join `re_account_spoken_languages` on `re_spoken_languages`.`id` = `re_account_spoken_languages`.`spoken_language_id` where `re_account_spoken_languages`.`account_id` = 167", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 276}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.1756172, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 10.708, "width_percent": 1.654}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `slugs`.`reference_id` = 167 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 288}], "start": **********.21888, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 12.362, "width_percent": 0.348}, {"sql": "select count(*) as aggregate from `re_properties` where `re_properties`.`author_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `re_properties`.`author_id` = 167 and `re_properties`.`author_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Account.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Account.php", "line": 280}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 26, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 288}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2241108, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Account.php:280", "source": {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Account.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Account.php", "line": 280}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:280", "ajax": false, "filename": "Account.php", "line": "280"}, "connection": "xmetr", "explain": null, "start_percent": 12.71, "width_percent": 0.339}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 167 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 294}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.227971, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 13.049, "width_percent": 1.21}, {"sql": "select * from `re_properties` where (`re_properties`.`moderation_status` = 'approved' and `re_properties`.`status` != 'not_available') and (`expire_date` >= '2025-07-19 18:58:30' or `never_expired` = 1) and `re_properties`.`project_id` = 25 order by `created_at` desc limit 8", "type": "query", "params": [], "bindings": ["approved", "not_available", "2025-07-19 18:58:30", 1, 25], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 507}, {"index": 18, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 349}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.298378, "duration": 0.00949, "duration_str": "9.49ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 14.259, "width_percent": 4.348}, {"sql": "select `id`, `key`, `prefix`, `reference_id` from `slugs` where `slugs`.`reference_id` in (101, 1172) and `slugs`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 507}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 349}], "start": **********.311354, "duration": 0.00675, "duration_str": "6.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 18.607, "width_percent": 3.093}, {"sql": "select `id`, `name` from `states` where `states`.`id` in (188)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 507}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 349}], "start": **********.322787, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.7, "width_percent": 0.417}, {"sql": "select `id`, `name` from `cities` where `cities`.`id` in (8175, 8189)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 507}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 349}], "start": **********.329334, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.117, "width_percent": 0.422}, {"sql": "select `id`, `is_default`, `exchange_rate`, `symbol`, `title`, `is_prefix_symbol` from `re_currencies` where `re_currencies`.`id` in (1, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 507}, {"index": 24, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 349}], "start": **********.383259, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 22.538, "width_percent": 0.49}, {"sql": "select `re_categories`.`id`, `re_categories`.`name`, `re_property_categories`.`property_id` as `pivot_property_id`, `re_property_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` in (101, 1172) and `status` = 'published' order by `created_at` desc, `is_default` desc, `order` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 21, "namespace": null, "name": "platform/plugins/real-estate/src/Repositories/Eloquent/PropertyRepository.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Repositories\\Eloquent\\PropertyRepository.php", "line": 507}, {"index": 22, "namespace": "view", "name": "theme.xmetr::views.real-estate.project", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/project.blade.php", "line": 349}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3893049, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.029, "width_percent": 0.513}, {"sql": "select * from `re_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/helpers/currencies.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\currencies.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.0659769, "duration": 0.03393, "duration_str": "33.93ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 23.542, "width_percent": 15.546}, {"sql": "select * from `countries` where `countries`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752951511.1668, "duration": 0.01652, "duration_str": "16.52ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 39.088, "width_percent": 7.569}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1172", "type": "query", "params": [], "bindings": [1172], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752951511.189832, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 46.658, "width_percent": 0.715}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.195675, "duration": 0.046189999999999995, "duration_str": "46.19ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 47.372, "width_percent": 21.164}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 15 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.249676, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 68.536, "width_percent": 0.669}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 16 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 16], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.2569559, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.205, "width_percent": 0.577}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 1172 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 107}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.263273, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 69.782, "width_percent": 0.77}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 135}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752951511.271826, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 70.552, "width_percent": 0.577}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 29 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 170}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.281699, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 71.129, "width_percent": 1.727}, {"sql": "select * from `countries` where `countries`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 92}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752951511.306753, "duration": 0.027010000000000003, "duration_str": "27.01ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 72.857, "width_percent": 12.376}, {"sql": "select `re_features`.*, `re_property_features`.`property_id` as `pivot_property_id`, `re_property_features`.`feature_id` as `pivot_feature_id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 101", "type": "query", "params": [], "bindings": [101], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 96}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752951511.340073, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 85.233, "width_percent": 0.619}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 8 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.345057, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 85.851, "width_percent": 1.173}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 13 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 13], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.3533258, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 87.024, "width_percent": 0.499}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Feature' and `meta_boxes`.`reference_id` = 14 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Feature", 14], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 99}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.359961, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 87.523, "width_percent": 0.33}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `meta_boxes`.`reference_id` = 101 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 101], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 107}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.368047, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 87.853, "width_percent": 1.292}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 135}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752951511.378895, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 89.145, "width_percent": 0.971}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `meta_boxes`.`reference_id` = 22 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 22], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.xmetr::views.real-estate.properties.item-grid", "file": "D:\\laragon\\www\\xmetr\\platform\\themes/xmetr/views/real-estate/properties/item-grid.blade.php", "line": 170}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1752951511.390071, "duration": 0.00788, "duration_str": "7.88ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 90.117, "width_percent": 3.611}, {"sql": "select `re_categories`.*, `re_project_categories`.`project_id` as `pivot_project_id`, `re_project_categories`.`category_id` as `pivot_category_id` from `re_categories` inner join `re_project_categories` on `re_categories`.`id` = `re_project_categories`.`category_id` where `re_project_categories`.`project_id` = 25", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Project.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Project.php", "line": 163}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 29, "namespace": null, "name": "platform/plugins/real-estate/src/Models/Project.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Models\\Project.php", "line": 181}, {"index": 36, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1752951511.486192, "duration": 0.00657, "duration_str": "6.57ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php:28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "xmetr", "explain": null, "start_percent": 93.727, "width_percent": 3.01}, {"sql": "select * from `ads`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 55}, {"index": 17, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 46}, {"index": 18, "namespace": null, "name": "platform/plugins/ads/src/Supports/AdsManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Supports\\AdsManager.php", "line": 29}, {"index": 20, "namespace": null, "name": "platform/plugins/ads/src/Providers/AdsServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\ads\\src\\Providers\\AdsServiceProvider.php", "line": 165}], "start": **********.014729, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.738, "width_percent": 2.328}, {"sql": "select * from `slugs` where `reference_id` = 25 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project' limit 1", "type": "query", "params": [], "bindings": [25, "Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 69}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4091432, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 99.065, "width_percent": 0.284}, {"sql": "select * from `slugs_translations` where `slugs_translations`.`slugs_id` in (205)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 69}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.411112, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 99.349, "width_percent": 0.202}, {"sql": "select * from `slugs` where `reference_id` = 25 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Project' limit 1", "type": "query", "params": [], "bindings": [25, "Xmetr\\RealEstate\\Models\\Project"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 74}, {"index": 18, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.41379, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "AddHrefLangListener.php:74", "source": {"index": 14, "namespace": null, "name": "platform/plugins/language/src/Listeners/AddHrefLangListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\AddHrefLangListener.php", "line": 74}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FListeners%2FAddHrefLangListener.php:74", "ajax": false, "filename": "AddHrefLangListener.php", "line": "74"}, "connection": "xmetr", "explain": null, "start_percent": 99.551, "width_percent": 0.22}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Providers\\HookServiceProvider.php", "line": 468}], "start": **********.592772, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 99.771, "width_percent": 0.229}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Currency": {"value": 45, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 17, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 16, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Project": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProject.php:1", "ajax": false, "filename": "Project.php", "line": "?"}}, "Xmetr\\LanguageAdvanced\\Models\\TranslationResolver": {"value": 4, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FModels%2FTranslationResolver.php:1", "ajax": false, "filename": "TranslationResolver.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\SpokenLanguage": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FSpokenLanguage.php:1", "ajax": false, "filename": "SpokenLanguage.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\Location\\Models\\Country": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php:1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\PropertyDailyView": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FPropertyDailyView.php:1", "ajax": false, "filename": "PropertyDailyView.php", "line": "?"}}, "Xmetr\\Location\\Models\\State": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FState.php:1", "ajax": false, "filename": "State.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 113, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/projects/castle-view", "action_name": null, "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getViewWithPrefix", "uri": "GET en/{prefix}/{slug?}", "controller": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getViewWithPrefix<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:114\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/en", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php:114\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/PublicController.php:114-117</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "28.65s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1407596168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1407596168\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-317169449 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-317169449\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1759259977 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://xmetr.gc/admin/real-estate/projects/edit/25</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; project_wishlist=25; wishlist=657; _hjSession_6417422=eyJpZCI6Ijg3Mjk5NzNiLTU1NjUtNDEyYi1hNTZlLTJlMTk4ODQzYTk2MCIsImMiOjE3NTI5NDgxNjUzNzUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; XSRF-TOKEN=eyJpdiI6IjloeHNWa1VjWEtqTVhEYithOWQxc2c9PSIsInZhbHVlIjoiQkViZHQ4eDdIVFFoWStJZnEzOHdGa2RhbUVwLzB6M3BzS1hmYy9vakg4MzYrbTI3b2RSTENzdksxQUdheCtHS2k2SzYvUk0ycG8zc0VFMHZMTzZwNGNqUFRsWkcyRVBGNWdwbThXNGV4cDF4T0FUbFA5c3lMRytESUVlemdZbEIiLCJtYWMiOiJmY2FhNWNlYzY2YzgzYjk4ZWFiYTczM2RiNDMzMzdkYzFiODJlNGMxYjRhNDZkMTdjMTExYjQ4OTQzY2Q0NDU0IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6Ikd2eGFiakViaGJ0bSs0WXVMT2NxQ2c9PSIsInZhbHVlIjoibnU1T2htK2I4bXVkQUZROFljUjRNMEZFMUJPVlJ0QnBiRTNVOFNKbExXTlE5bS9XdGtUSThhTFRMdTBCUWErZmpxWWFPZGZLTm1Fc3EvUTVZdG9hOC9JT1I0VVh0eW45bk1YRzMyS2FBMUcrWEMrNmk1Z1RnRGNpZXFjLzNNOUYiLCJtYWMiOiJmYTY3MTZkZTdiMWQ4MDQzNWYzODZkOTA0YjRlOGFkZmU1NjFhYWQ4YjRhMDc2ZDMwNjc5MTk2YTYzMWJmZDgxIiwidGFnIjoiIn0%3D; _ga_KQ6X76DET4=GS2.1.s1752948165$o95$g1$t1752950947$j56$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759259977\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1749462901 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RobK1zu3t1m8xEzREPlVbKwr1yqlht8JLepj3TJp</span>\"\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749462901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-554045091 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 18:58:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>xmetr-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.4.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>developer-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Ishtiaq Ahmed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>project-name</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">Xmetr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554045091\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-914457105 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">https://xmetr.gc/en/projects/castle-view</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914457105\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/projects/castle-view", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\PublicController@getViewWithPrefix"}, "badge": null}}