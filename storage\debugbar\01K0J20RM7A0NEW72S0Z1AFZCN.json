{"__meta": {"id": "01K0J20RM7A0NEW72S0Z1AFZCN", "datetime": "2025-07-19 19:19:18", "utime": **********.921481, "method": "POST", "uri": "/admin/theme/options", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752952752.237335, "end": **********.921518, "duration": 6.684183120727539, "duration_str": "6.68s", "measures": [{"label": "Booting", "start": 1752952752.237335, "relative_start": 0, "end": **********.879528, "relative_end": **********.879528, "duration": 1.***************, "duration_str": "1.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.879583, "relative_start": 1.****************, "end": **********.921522, "relative_end": 3.814697265625e-06, "duration": 5.**************, "duration_str": "5.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.938278, "relative_start": 1.****************, "end": **********.991821, "relative_end": **********.991821, "duration": 0.****************, "duration_str": "53.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.913889, "relative_start": 6.***************, "end": **********.91698, "relative_end": **********.91698, "duration": 0.003091096878051758, "duration_str": "3.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 847, "nb_visible_statements": 500, "nb_excluded_statements": 347, "nb_failed_statements": 0, "accumulated_duration": 2.479099999999999, "accumulated_duration_str": "2.48s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.0206141, "duration": 0.01066, "duration_str": "10.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.43}, {"sql": "select `value`, `key` from `settings` where `key` like 'theme-xmetr-%'", "type": "query", "params": [], "bindings": ["theme-xmetr-%"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 516}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 525}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/FormRequest.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\FormRequest.php", "line": 91}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/ValidatesWhenResolvedTrait.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php", "line": 25}], "start": **********.051316, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "ThemeOption.php:516", "source": {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 516}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FThemeOption.php:516", "ajax": false, "filename": "ThemeOption.php", "line": "516"}, "connection": "xmetr", "explain": null, "start_percent": 0.43, "width_percent": 0.202}, {"sql": "select `name`, `id` from `pages` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 90}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.093297, "duration": 0.00584, "duration_str": "5.84ms", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:46", "source": {"index": 14, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 46}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FProviders%2FHookServiceProvider.php:46", "ajax": false, "filename": "HookServiceProvider.php", "line": "46"}, "connection": "xmetr", "explain": null, "start_percent": 0.632, "width_percent": 0.236}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 18, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.142426, "duration": 0.02265, "duration_str": "22.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:45", "source": {"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:45", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "45"}, "connection": "xmetr", "explain": null, "start_percent": 0.868, "width_percent": 0.914}, {"sql": "update `settings` set `value` = '6eadd395dd057399bda782a98b2db2e1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'media_random_hash'", "type": "query", "params": [], "bindings": ["6eadd395dd057399bda782a98b2db2e1", "2025-07-19 19:19:14", "media_random_hash"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.169981, "duration": 0.01755, "duration_str": "17.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 1.782, "width_percent": 0.708}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'api_enabled'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "api_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.190353, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.49, "width_percent": 0.177}, {"sql": "update `settings` set `value` = '[\\\"language\\\",\\\"language-advanced\\\",\\\"ads\\\",\\\"analytics\\\",\\\"audit-log\\\",\\\"backup\\\",\\\"captcha\\\",\\\"contact\\\",\\\"cookie-consent\\\",\\\"faq\\\",\\\"location\\\",\\\"newsletter\\\",\\\"payment\\\",\\\"paypal\\\",\\\"paystack\\\",\\\"razorpay\\\",\\\"real-estate\\\",\\\"social-login\\\",\\\"sslcommerz\\\",\\\"stripe\\\",\\\"testimonial\\\",\\\"translation\\\",\\\"magic\\\"]', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'activated_plugins'", "type": "query", "params": [], "bindings": ["[\"language\",\"language-advanced\",\"ads\",\"analytics\",\"audit-log\",\"backup\",\"captcha\",\"contact\",\"cookie-consent\",\"faq\",\"location\",\"newsletter\",\"payment\",\"paypal\",\"paystack\",\"razorpay\",\"real-estate\",\"social-login\",\"sslcommerz\",\"stripe\",\"testimonial\",\"translation\",\"magic\"]", "2025-07-19 19:19:14", "activated_plugins"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1981242, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.666, "width_percent": 0.209}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'analytics_dashboard_widgets'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "analytics_dashboard_widgets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2066262, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.875, "width_percent": 0.181}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'enable_recaptcha_xmetr_contact_forms_fronts_contact_form'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "enable_recaptcha_xmetr_contact_forms_fronts_contact_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2138782, "duration": 0.00625, "duration_str": "6.25ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.056, "width_percent": 0.252}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'enable_recaptcha_xmetr_newsletter_forms_fronts_newsletter_form'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "enable_recaptcha_xmetr_newsletter_forms_fronts_newsletter_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.222922, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.308, "width_percent": 0.184}, {"sql": "update `settings` set `value` = '[\\\"email\\\"]', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'real_estate_mandatory_fields_at_consult_form'", "type": "query", "params": [], "bindings": ["[\"email\"]", "2025-07-19 19:19:14", "real_estate_mandatory_fields_at_consult_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2299628, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.492, "width_percent": 0.18}, {"sql": "update `settings` set `value` = 'xmetr', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme'", "type": "query", "params": [], "bindings": ["xmetr", "2025-07-19 19:19:14", "theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.237027, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.672, "width_percent": 0.178}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'show_admin_bar'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "show_admin_bar"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2441711, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.851, "width_percent": 0.19}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'language_hide_default'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "language_hide_default"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.251302, "duration": 0.0063, "duration_str": "6.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.041, "width_percent": 0.254}, {"sql": "update `settings` set `value` = 'dropdown', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'language_switcher_display'", "type": "query", "params": [], "bindings": ["dropdown", "2025-07-19 19:19:14", "language_switcher_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.260647, "duration": 0.01528, "duration_str": "15.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.295, "width_percent": 0.616}, {"sql": "update `settings` set `value` = 'all', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'language_display'", "type": "query", "params": [], "bindings": ["all", "2025-07-19 19:19:14", "language_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.278463, "duration": 0.00633, "duration_str": "6.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.911, "width_percent": 0.255}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'language_hide_languages'", "type": "query", "params": [], "bindings": ["[]", "2025-07-19 19:19:14", "language_hide_languages"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.287237, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.166, "width_percent": 0.186}, {"sql": "update `settings` set `value` = 'news', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'permalink-xmetr-blog-models-post'", "type": "query", "params": [], "bindings": ["news", "2025-07-19 19:19:14", "permalink-xmetr-blog-models-post"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.294276, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.352, "width_percent": 0.209}, {"sql": "update `settings` set `value` = 'news', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'permalink-xmetr-blog-models-category'", "type": "query", "params": [], "bindings": ["news", "2025-07-19 19:19:14", "permalink-xmetr-blog-models-category"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3026068, "duration": 0.005860000000000001, "duration_str": "5.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.561, "width_percent": 0.236}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'payment_cod_status'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "payment_cod_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.311408, "duration": 0.00601, "duration_str": "6.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.797, "width_percent": 0.242}, {"sql": "update `settings` set `value` = 'Please pay money directly to the postman, if you choose cash on delivery method (COD).', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'payment_cod_description'", "type": "query", "params": [], "bindings": ["Please pay money directly to the postman, if you choose cash on delivery method (COD).", "2025-07-19 19:19:14", "payment_cod_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3210201, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.04, "width_percent": 0.203}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'payment_bank_transfer_status'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "payment_bank_transfer_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3286219, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.243, "width_percent": 0.187}, {"sql": "update `settings` set `value` = 'Please send money to our bank account: ACB - 69270 213 19.', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'payment_bank_transfer_description'", "type": "query", "params": [], "bindings": ["Please send money to our bank account: ACB - 69270 213 19.", "2025-07-19 19:19:14", "payment_bank_transfer_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3371449, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.43, "width_percent": 0.208}, {"sql": "update `settings` set `value` = 'stripe_checkout', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'payment_stripe_payment_type'", "type": "query", "params": [], "bindings": ["stripe_checkout", "2025-07-19 19:19:14", "payment_stripe_payment_type"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.344972, "duration": 0.0048200000000000005, "duration_str": "4.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.637, "width_percent": 0.194}, {"sql": "update `settings` set `value` = 'XMetr – долгосрочная аренда недвижимости в Аргентине', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-site_title'", "type": "query", "params": [], "bindings": ["XMetr – долгосрочная аренда недвижимости в Аргентине", "2025-07-19 19:19:14", "theme-homzen-site_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.353375, "duration": 0.004900000000000001, "duration_str": "4.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.832, "width_percent": 0.198}, {"sql": "update `settings` set `value` = 'Долгосрочная аренда квартир в Аргентине и Буэнос-Айресе.', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-seo_description'", "type": "query", "params": [], "bindings": ["Долгосрочная аренда квартир в Аргентине и Буэнос-Айресе.", "2025-07-19 19:19:14", "theme-homzen-seo_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.360937, "duration": 0.00483, "duration_str": "4.83ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.03, "width_percent": 0.195}, {"sql": "update `settings` set `value` = '©XMetr - все права защищены.', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-copyright'", "type": "query", "params": [], "bindings": ["©XMetr - все права защищены.", "2025-07-19 19:19:14", "theme-homzen-copyright"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.368474, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.224, "width_percent": 0.199}, {"sql": "update `settings` set `value` = 'general/favicon-1.png', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-favicon'", "type": "query", "params": [], "bindings": ["general/favicon-1.png", "2025-07-19 19:19:14", "theme-homzen-favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.376151, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.423, "width_percent": 0.184}, {"sql": "update `settings` set `value` = 'general/header-logo2.png', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-logo'", "type": "query", "params": [], "bindings": ["general/header-logo2.png", "2025-07-19 19:19:14", "theme-homzen-logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3840458, "duration": 0.00654, "duration_str": "6.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.607, "width_percent": 0.264}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-logo_light'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-07-19 19:19:14", "theme-homzen-logo_light"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.393046, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.871, "width_percent": 0.186}, {"sql": "update `settings` set `value` = 'no', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-preloader_enabled'", "type": "query", "params": [], "bindings": ["no", "2025-07-19 19:19:14", "theme-homzen-preloader_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.400497, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.057, "width_percent": 0.186}, {"sql": "update `settings` set `value` = 'v2', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-preloader_version'", "type": "query", "params": [], "bindings": ["v2", "2025-07-19 19:19:14", "theme-homzen-preloader_version"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.408499, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.244, "width_percent": 0.228}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Telegram\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-telegram\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/t.me\\\\/xmetrcom\\\"},{\\\"key\\\":\\\"image\\\",\\\"value\\\":null},{\\\"key\\\":\\\"color\\\",\\\"value\\\":\\\"transparent\\\"},{\\\"key\\\":\\\"background-color\\\",\\\"value\\\":\\\"transparent\\\"}]]', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-social_links'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"name\",\"value\":\"Telegram\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-telegram\"},{\"key\":\"url\",\"value\":\"https:\\/\\/t.me\\/xmetrcom\"},{\"key\":\"image\",\"value\":null},{\"key\":\"color\",\"value\":\"transparent\"},{\"key\":\"background-color\",\"value\":\"transparent\"}]]", "2025-07-19 19:19:14", "theme-homzen-social_links"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.417244, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.471, "width_percent": 0.22}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"x\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"pinterest\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-pinterest\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"linkedin\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"whatsapp\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-whatsapp\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"email\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-mail\\\"}]]', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-social_sharing'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"social\",\"value\":\"facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"}],[{\"key\":\"social\",\"value\":\"x\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"}],[{\"key\":\"social\",\"value\":\"pinterest\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-pinterest\"}],[{\"key\":\"social\",\"value\":\"linkedin\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"}],[{\"key\":\"social\",\"value\":\"whatsapp\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-whatsapp\"}],[{\"key\":\"social\",\"value\":\"email\"},{\"key\":\"icon\",\"value\":\"ti ti-mail\"}]]", "2025-07-19 19:19:14", "theme-homzen-social_sharing"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.425474, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.691, "width_percent": 0.213}, {"sql": "update `settings` set `value` = 'rgb(103, 28, 201)', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-primary_color'", "type": "query", "params": [], "bindings": ["rgb(103, 28, 201)", "2025-07-19 19:19:14", "theme-homzen-primary_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.433396, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.904, "width_percent": 0.218}, {"sql": "update `settings` set `value` = 'rgb(101, 28, 197)', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-hover_color'", "type": "query", "params": [], "bindings": ["rgb(101, 28, 197)", "2025-07-19 19:19:14", "theme-homzen-hover_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.441238, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.122, "width_percent": 0.179}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-footer_background_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-07-19 19:19:14", "theme-homzen-footer_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.448313, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.301, "width_percent": 0.17}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-footer_background_image'", "type": "query", "params": [], "bindings": ["", "2025-07-19 19:19:14", "theme-homzen-footer_background_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.454868, "duration": 0.0058200000000000005, "duration_str": "5.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.471, "width_percent": 0.235}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-use_modal_for_authentication'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "theme-homzen-use_modal_for_authentication"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.463151, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.706, "width_percent": 0.174}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-homepage_id'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "theme-homzen-homepage_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.470251, "duration": 0.00511, "duration_str": "5.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.88, "width_percent": 0.206}, {"sql": "update `settings` set `value` = '6', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-blog_page_id'", "type": "query", "params": [], "bindings": ["6", "2025-07-19 19:19:14", "theme-homzen-blog_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.47816, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.086, "width_percent": 0.178}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-hotline'", "type": "query", "params": [], "bindings": ["", "2025-07-19 19:19:14", "theme-homzen-hotline"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4856699, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.265, "width_percent": 0.209}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-email'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-07-19 19:19:14", "theme-homzen-email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4940138, "duration": 0.005690000000000001, "duration_str": "5.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.473, "width_percent": 0.23}, {"sql": "update `settings` set `value` = '#f7f7f7', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-breadcrumb_background_color'", "type": "query", "params": [], "bindings": ["#f7f7f7", "2025-07-19 19:19:14", "theme-homzen-breadcrumb_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.502355, "duration": 0.00492, "duration_str": "4.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.703, "width_percent": 0.198}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-breadcrumb_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-07-19 19:19:14", "theme-homzen-breadcrumb_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.510784, "duration": 0.00649, "duration_str": "6.49ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.901, "width_percent": 0.262}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-lazy_load_images'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "theme-homzen-lazy_load_images"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.520013, "duration": 0.004860000000000001, "duration_str": "4.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.163, "width_percent": 0.196}, {"sql": "update `settings` set `value` = 'general/placeholder.png', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-lazy_load_placeholder_image'", "type": "query", "params": [], "bindings": ["general/placeholder.png", "2025-07-19 19:19:14", "theme-homzen-lazy_load_placeholder_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.527407, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.359, "width_percent": 0.194}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-newsletter_popup_enable'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "theme-homzen-newsletter_popup_enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5347311, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.553, "width_percent": 0.202}, {"sql": "update `settings` set `value` = 'general/newsletter-image.jpg', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-newsletter_popup_image'", "type": "query", "params": [], "bindings": ["general/newsletter-image.jpg", "2025-07-19 19:19:14", "theme-homzen-newsletter_popup_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.542787, "duration": 0.00694, "duration_str": "6.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.755, "width_percent": 0.28}, {"sql": "update `settings` set `value` = 'Let’s join our newsletter!', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-newsletter_popup_title'", "type": "query", "params": [], "bindings": ["Let’s join our newsletter!", "2025-07-19 19:19:14", "theme-homzen-newsletter_popup_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.55267, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.035, "width_percent": 0.175}, {"sql": "update `settings` set `value` = 'Weekly Updates', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-newsletter_popup_subtitle'", "type": "query", "params": [], "bindings": ["Weekly Updates", "2025-07-19 19:19:14", "theme-homzen-newsletter_popup_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.559661, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.21, "width_percent": 0.228}, {"sql": "update `settings` set `value` = 'Do not worry we don’t spam!', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-newsletter_popup_description'", "type": "query", "params": [], "bindings": ["Do not worry we don’t spam!", "2025-07-19 19:19:14", "theme-homzen-newsletter_popup_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5680299, "duration": 0.0051600000000000005, "duration_str": "5.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.438, "width_percent": 0.208}, {"sql": "update `settings` set `value` = '14', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-properties_list_page_id'", "type": "query", "params": [], "bindings": ["14", "2025-07-19 19:19:14", "theme-homzen-properties_list_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.575974, "duration": 0.00479, "duration_str": "4.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.646, "width_percent": 0.193}, {"sql": "update `settings` set `value` = '15', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-projects_list_page_id'", "type": "query", "params": [], "bindings": ["15", "2025-07-19 19:19:14", "theme-homzen-projects_list_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5835, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.839, "width_percent": 0.179}, {"sql": "update `settings` set `value` = '2024-10-09 15:29:15', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'membership_authorization_at'", "type": "query", "params": [], "bindings": ["2024-10-09 15:29:15", "2025-07-19 19:19:14", "membership_authorization_at"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5903351, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.018, "width_percent": 0.169}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'is_completed_get_started'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "is_completed_get_started"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.597725, "duration": 0.00492, "duration_str": "4.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.187, "width_percent": 0.198}, {"sql": "update `settings` set `value` = 'no', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-enabled_back_to_top'", "type": "query", "params": [], "bindings": ["no", "2025-07-19 19:19:14", "theme-homzen-enabled_back_to_top"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.605386, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.386, "width_percent": 0.213}, {"sql": "update `settings` set `value` = 'M d, Y', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-date_format'", "type": "query", "params": [], "bindings": ["M d, Y", "2025-07-19 19:19:14", "theme-homzen-date_format"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.61368, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.598, "width_percent": 0.186}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-show_site_name'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "theme-homzen-show_site_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6219301, "duration": 0.00519, "duration_str": "5.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.785, "width_percent": 0.209}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-seo_title'", "type": "query", "params": [], "bindings": ["", "2025-07-19 19:19:14", "theme-homzen-seo_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.630289, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.994, "width_percent": 0.196}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-seo_index'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "theme-homzen-seo_index"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.638326, "duration": 0.005809999999999999, "duration_str": "5.81ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.19, "width_percent": 0.234}, {"sql": "update `settings` set `value` = 'general/header-logo2.png', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-seo_og_image'", "type": "query", "params": [], "bindings": ["general/header-logo2.png", "2025-07-19 19:19:14", "theme-homzen-seo_og_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.647059, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.424, "width_percent": 0.184}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-term_and_privacy_policy_url'", "type": "query", "params": [], "bindings": ["", "2025-07-19 19:19:14", "theme-homzen-term_and_privacy_policy_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.655681, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.608, "width_percent": 0.208}, {"sql": "update `settings` set `value` = 'Roboto', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_primary_font'", "type": "query", "params": [], "bindings": ["Roboto", "2025-07-19 19:19:14", "theme-homzen-tp_primary_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.663576, "duration": 0.00477, "duration_str": "4.77ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.816, "width_percent": 0.192}, {"sql": "update `settings` set `value` = 'Roboto', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_heading_font'", "type": "query", "params": [], "bindings": ["Roboto", "2025-07-19 19:19:14", "theme-homzen-tp_heading_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.67091, "duration": 0.005849999999999999, "duration_str": "5.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.008, "width_percent": 0.236}, {"sql": "update `settings` set `value` = '80', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_h1_size'", "type": "query", "params": [], "bindings": ["80", "2025-07-19 19:19:14", "theme-homzen-tp_h1_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.679719, "duration": 0.00479, "duration_str": "4.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.244, "width_percent": 0.193}, {"sql": "update `settings` set `value` = '55', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_h2_size'", "type": "query", "params": [], "bindings": ["55", "2025-07-19 19:19:14", "theme-homzen-tp_h2_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6871428, "duration": 0.00479, "duration_str": "4.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.437, "width_percent": 0.193}, {"sql": "update `settings` set `value` = '40', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_h3_size'", "type": "query", "params": [], "bindings": ["40", "2025-07-19 19:19:14", "theme-homzen-tp_h3_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.694741, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.631, "width_percent": 0.182}, {"sql": "update `settings` set `value` = '35', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_h4_size'", "type": "query", "params": [], "bindings": ["35", "2025-07-19 19:19:14", "theme-homzen-tp_h4_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.701954, "duration": 0.00479, "duration_str": "4.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.813, "width_percent": 0.193}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_h5_size'", "type": "query", "params": [], "bindings": ["30", "2025-07-19 19:19:14", "theme-homzen-tp_h5_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.709642, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.006, "width_percent": 0.184}, {"sql": "update `settings` set `value` = '20', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_h6_size'", "type": "query", "params": [], "bindings": ["20", "2025-07-19 19:19:14", "theme-homzen-tp_h6_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7170658, "duration": 0.0064, "duration_str": "6.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.191, "width_percent": 0.258}, {"sql": "update `settings` set `value` = '15', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-tp_body_size'", "type": "query", "params": [], "bindings": ["15", "2025-07-19 19:19:14", "theme-homzen-tp_body_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.725934, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.449, "width_percent": 0.202}, {"sql": "update `settings` set `value` = '20', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-number_of_projects_per_page'", "type": "query", "params": [], "bindings": ["20", "2025-07-19 19:19:14", "theme-homzen-number_of_projects_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.734168, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.651, "width_percent": 0.211}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-number_of_properties_per_page'", "type": "query", "params": [], "bindings": ["30", "2025-07-19 19:19:14", "theme-homzen-number_of_properties_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.741821, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.862, "width_percent": 0.169}, {"sql": "update `settings` set `value` = '8', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-number_of_related_projects'", "type": "query", "params": [], "bindings": ["8", "2025-07-19 19:19:14", "theme-homzen-number_of_related_projects"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.748655, "duration": 0.00697, "duration_str": "6.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.031, "width_percent": 0.281}, {"sql": "update `settings` set `value` = '8', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-number_of_related_properties'", "type": "query", "params": [], "bindings": ["8", "2025-07-19 19:19:14", "theme-homzen-number_of_related_properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.75867, "duration": 0.007549999999999999, "duration_str": "7.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.312, "width_percent": 0.305}, {"sql": "update `settings` set `value` = '43.615134, -76.393186', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-latitude_longitude_center_on_properties_page'", "type": "query", "params": [], "bindings": ["43.615134, -76.393186", "2025-07-19 19:19:14", "theme-homzen-latitude_longitude_center_on_properties_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7696211, "duration": 0.0057599999999999995, "duration_str": "5.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.617, "width_percent": 0.232}, {"sql": "update `settings` set `value` = 'top-map', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-real_estate_property_listing_layout'", "type": "query", "params": [], "bindings": ["top-map", "2025-07-19 19:19:14", "theme-homzen-real_estate_property_listing_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.778308, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.849, "width_percent": 0.194}, {"sql": "update `settings` set `value` = '3', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-real_estate_property_detail_layout'", "type": "query", "params": [], "bindings": ["3", "2025-07-19 19:19:14", "theme-homzen-real_estate_property_detail_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.78688, "duration": 0.00484, "duration_str": "4.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.043, "width_percent": 0.195}, {"sql": "update `settings` set `value` = 'top-map', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-real_estate_project_listing_layout'", "type": "query", "params": [], "bindings": ["top-map", "2025-07-19 19:19:14", "theme-homzen-real_estate_project_listing_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.794588, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.238, "width_percent": 0.191}, {"sql": "update `settings` set `value` = 'yes', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-real_estate_show_map_on_single_detail_page'", "type": "query", "params": [], "bindings": ["yes", "2025-07-19 19:19:14", "theme-homzen-real_estate_show_map_on_single_detail_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.801852, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.429, "width_percent": 0.186}, {"sql": "update `settings` set `value` = 'loichenko', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'licensed_to'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "2025-07-19 19:19:14", "licensed_to"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.808771, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.616, "width_percent": 0.165}, {"sql": "update `settings` set `value` = '[\\\"<EMAIL>\\\"]', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'admin_email'", "type": "query", "params": [], "bindings": ["[\"<EMAIL>\"]", "2025-07-19 19:19:14", "admin_email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.815433, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.781, "width_percent": 0.228}, {"sql": "update `settings` set `value` = 'UTC', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'time_zone'", "type": "query", "params": [], "bindings": ["UTC", "2025-07-19 19:19:14", "time_zone"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8240778, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.009, "width_percent": 0.186}, {"sql": "update `settings` set `value` = 'ltr', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'locale_direction'", "type": "query", "params": [], "bindings": ["ltr", "2025-07-19 19:19:14", "locale_direction"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.831126, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.194, "width_percent": 0.184}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'enable_send_error_reporting_via_email'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "enable_send_error_reporting_via_email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.839248, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.378, "width_percent": 0.225}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'redirect_404_to_homepage'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "redirect_404_to_homepage"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.847627, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.603, "width_percent": 0.197}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'audit_log_data_retention_period'", "type": "query", "params": [], "bindings": ["30", "2025-07-19 19:19:14", "audit_log_data_retention_period"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.856134, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.8, "width_percent": 0.179}, {"sql": "update `settings` set `value` = 'ru', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'locale'", "type": "query", "params": [], "bindings": ["ru", "2025-07-19 19:19:14", "locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8630252, "duration": 0.00504, "duration_str": "5.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.979, "width_percent": 0.203}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'language_show_default_item_if_current_version_not_existed'", "type": "query", "params": [], "bindings": ["1", "2025-07-19 19:19:14", "language_show_default_item_if_current_version_not_existed"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.871633, "duration": 0.00574, "duration_str": "5.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.182, "width_percent": 0.232}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'language_auto_detect_user_language'", "type": "query", "params": [], "bindings": ["0", "2025-07-19 19:19:14", "language_auto_detect_user_language"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.87999, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.414, "width_percent": 0.184}, {"sql": "update `settings` set `value` = '#f7f7f7', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-top_header_background_color'", "type": "query", "params": [], "bindings": ["#f7f7f7", "2025-07-19 19:19:14", "theme-homzen-top_header_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.88836, "duration": 0.00809, "duration_str": "8.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.598, "width_percent": 0.326}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-top_header_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-07-19 19:19:14", "theme-homzen-top_header_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.898907, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.924, "width_percent": 0.19}, {"sql": "update `settings` set `value` = '#ffffff', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-main_header_background_color'", "type": "query", "params": [], "bindings": ["#ffffff", "2025-07-19 19:19:14", "theme-homzen-main_header_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.907666, "duration": 0.00455, "duration_str": "4.55ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.114, "width_percent": 0.184}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-main_header_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-07-19 19:19:14", "theme-homzen-main_header_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.914565, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.298, "width_percent": 0.19}, {"sql": "update `settings` set `value` = '#e4e4e4', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-main_header_border_color'", "type": "query", "params": [], "bindings": ["#e4e4e4", "2025-07-19 19:19:14", "theme-homzen-main_header_border_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.92167, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.488, "width_percent": 0.2}, {"sql": "update `settings` set `value` = 'yes', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-cookie_consent_enable'", "type": "query", "params": [], "bindings": ["yes", "2025-07-19 19:19:14", "theme-homzen-cookie_consent_enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.928868, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.688, "width_percent": 0.196}, {"sql": "update `settings` set `value` = 'minimal', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-cookie_consent_style'", "type": "query", "params": [], "bindings": ["minimal", "2025-07-19 19:19:14", "theme-homzen-cookie_consent_style"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.937847, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.884, "width_percent": 0.177}, {"sql": "update `settings` set `value` = '? Мы используем cookies, что бы сделать сайт лучше.', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-cookie_consent_message'", "type": "query", "params": [], "bindings": ["? Мы используем cookies, что бы сделать сайт лучше.", "2025-07-19 19:19:14", "theme-homzen-cookie_consent_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9454122, "duration": 0.00539, "duration_str": "5.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.061, "width_percent": 0.217}, {"sql": "update `settings` set `value` = 'Ок', `settings`.`updated_at` = '2025-07-19 19:19:14' where `key` = 'theme-homzen-cookie_consent_button_text'", "type": "query", "params": [], "bindings": ["Ок", "2025-07-19 19:19:14", "theme-homzen-cookie_consent_button_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/theme/src/ThemeOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\ThemeOption.php", "line": 522}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.953003, "duration": 0.00475, "duration_str": "4.75ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php:55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 22.278, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960292, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.47, "width_percent": 0.169}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9650428, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.639, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.96968, "duration": 0.00598, "duration_str": "5.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.804, "width_percent": 0.241}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976269, "duration": 0.01307, "duration_str": "13.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 23.045, "width_percent": 0.527}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9899201, "duration": 0.19515000000000002, "duration_str": "195ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 23.573, "width_percent": 7.872}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.1858299, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.444, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.1905022, "duration": 0.00483, "duration_str": "4.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.609, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.1960578, "duration": 0.022019999999999998, "duration_str": "22.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.804, "width_percent": 0.888}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.218649, "duration": 0.039689999999999996, "duration_str": "39.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.692, "width_percent": 1.601}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.258946, "duration": 0.00859, "duration_str": "8.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.293, "width_percent": 0.346}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.268181, "duration": 0.018949999999999998, "duration_str": "18.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.64, "width_percent": 0.764}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.2877371, "duration": 0.01838, "duration_str": "18.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.404, "width_percent": 0.741}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.3066828, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.145, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.311115, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.303, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.3153338, "duration": 0.00512, "duration_str": "5.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.454, "width_percent": 0.207}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.321025, "duration": 0.01703, "duration_str": "17.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.66, "width_percent": 0.687}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.338641, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.347, "width_percent": 0.2}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.344149, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.548, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.34858, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.703, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.353082, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.861, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.358287, "duration": 0.00731, "duration_str": "7.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.046, "width_percent": 0.295}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.366201, "duration": 0.00705, "duration_str": "7.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.341, "width_percent": 0.284}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.3738549, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.626, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.378309, "duration": 0.01105, "duration_str": "11.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.783, "width_percent": 0.446}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.389821, "duration": 0.012039999999999999, "duration_str": "12.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.229, "width_percent": 0.486}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.402248, "duration": 0.01126, "duration_str": "11.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.715, "width_percent": 0.454}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.413965, "duration": 0.01102, "duration_str": "11.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.169, "width_percent": 0.445}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.425343, "duration": 0.012119999999999999, "duration_str": "12.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.614, "width_percent": 0.489}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.437871, "duration": 0.01177, "duration_str": "11.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.102, "width_percent": 0.475}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.450096, "duration": 0.01141, "duration_str": "11.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.577, "width_percent": 0.46}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.4618502, "duration": 0.011, "duration_str": "11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.037, "width_percent": 0.444}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.473211, "duration": 0.01128, "duration_str": "11.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.481, "width_percent": 0.455}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.48494, "duration": 0.01192, "duration_str": "11.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.936, "width_percent": 0.481}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.497329, "duration": 0.011970000000000001, "duration_str": "11.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.417, "width_percent": 0.483}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.5097191, "duration": 0.00578, "duration_str": "5.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.9, "width_percent": 0.233}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.515842, "duration": 0.0067, "duration_str": "6.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.133, "width_percent": 0.27}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.523006, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.403, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.527319, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.56, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.5312898, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.707, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.535231, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.851, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.539302, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.999, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.543455, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.152, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.547632, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.304, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.551735, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.453, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.5559149, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.604, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.560242, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.762, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.56431, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.912, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.568413, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.066, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.573679, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.262, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.577912, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.416, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.581991, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.569, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.5864048, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.73, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.590631, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.881, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.594786, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.035, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.598859, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.184, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.603144, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.34, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.607398, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.491, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.6119092, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.655, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.6162848, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.816, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.6206388, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.974, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.625191, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.138, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.6294081, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.29, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.63373, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.443, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.638255, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.6, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.642701, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.759, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.6470659, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.917, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.651429, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.074, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.655632, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.226, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.659961, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.382, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.664337, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.541, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.6686502, "duration": 0.00608, "duration_str": "6.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.697, "width_percent": 0.245}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.675179, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.943, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.679267, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.095, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.683341, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.244, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.6874168, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.394, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.69168, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.546, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.696057, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.705, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.700105, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.851, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.704223, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.999, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.708437, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.149, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.712682, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.305, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.716752, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.456, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.7207968, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.602, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.725137, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.753, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.729637, "duration": 0.00691, "duration_str": "6.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.916, "width_percent": 0.279}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.736941, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.195, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.741003, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.343, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.744991, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.491, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.748892, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.636, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.7529068, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.786, "width_percent": 0.138}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.756689, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.924, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.760663, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.071, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.7647161, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.221, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.768806, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.367, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.7726831, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.509, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.776701, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.655, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.78051, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.796, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.784375, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.939, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.788009, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.075, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.791782, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.215, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.795994, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.367, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.800237, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.522, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.8043761, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.673, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.80858, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.827, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.813007, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.984, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.817651, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.144, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.8222911, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.304, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.826843, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.466, "width_percent": 0.169}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.8315399, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.634, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.8354928, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.779, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.839865, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.936, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.8441858, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.091, "width_percent": 0.173}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.848975, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.263, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.853208, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.418, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.857243, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.56, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.861655, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.722, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.865851, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.874, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.869984, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.022, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.874093, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.173, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.878273, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.326, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.882395, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.477, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.886384, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.618, "width_percent": 0.208}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.892122, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.826, "width_percent": 0.17}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.896848, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.996, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.901564, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.168, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.906224, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.333, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.910676, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.495, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.9147751, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.645, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.918886, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.791, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.922769, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.933, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.926965, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.082, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.931271, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.229, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.936432, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.389, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.940822, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.547, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.945425, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.712, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.949728, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.867, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.9537299, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.013, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.957814, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.157, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.962471, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.319, "width_percent": 0.171}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.9672222, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.49, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.9717941, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.657, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.975911, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.802, "width_percent": 0.17}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.980684, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.972, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.984783, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.115, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.989179, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.27, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.993396, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.421, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952755.997682, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.57, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.002544, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.746, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.006679, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.897, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.0111291, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.05, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.015856, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.209, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.020427, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.365, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.025581, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.552, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.030025, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.709, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.034173, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.86, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.0384312, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.015, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.042779, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.169, "width_percent": 0.162}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.0472858, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.331, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.051214, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.476, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.0549881, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.615, "width_percent": 0.134}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.05866, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.748, "width_percent": 0.131}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.06237, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.879, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.0660682, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.015, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.069874, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.155, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.073689, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.295, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.07777, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.442, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.081995, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.596, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.085986, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.742, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.089891, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.885, "width_percent": 0.133}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.09372, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.018, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.0979002, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.166, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.102195, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.319, "width_percent": 0.138}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.106115, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.457, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.110322, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.61, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.114208, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.752, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.118582, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.901, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.12331, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.064, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.1274981, "duration": 0.00483, "duration_str": "4.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.21, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.132785, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.404, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.137635, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.581, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.14245, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.746, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.147636, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.929, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.152651, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.115, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.15678, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.263, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.1607618, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.403, "width_percent": 0.173}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.165537, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.576, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.1697428, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.731, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.1738489, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.882, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.17763, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.019, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.1819701, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.176, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.1859941, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.322, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.1900098, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.47, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.193963, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.613, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.198529, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.777, "width_percent": 0.168}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.203054, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.945, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2075489, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.11, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.211597, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.26, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.215653, "duration": 0.00719, "duration_str": "7.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.404, "width_percent": 0.29}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.223333, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.694, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2272909, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.838, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2317202, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.987, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.236375, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.154, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.240807, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.317, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2450838, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.465, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.249179, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.616, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.253176, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.76, "width_percent": 0.135}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2567759, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.895, "width_percent": 0.137}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2605488, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.032, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2645109, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.177, "width_percent": 0.159}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.268882, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.335, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.273793, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.521, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.27771, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.662, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.281806, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.809, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.285958, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.961, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.289823, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.102, "width_percent": 0.138}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2936108, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.241, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.2978091, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.392, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.30177, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.538, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.3056102, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.68, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.309499, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.819, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.313329, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.961, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.317333, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.108, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.3212252, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.254, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.325136, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.4, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.329292, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.55, "width_percent": 0.164}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.333714, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.714, "width_percent": 0.171}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.338357, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.885, "width_percent": 0.134}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.342098, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.018, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.345797, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.155, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.3495681, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.294, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.353351, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.434, "width_percent": 0.138}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.357105, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.572, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.3609872, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.715, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.364772, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.854, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.368614, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.993, "width_percent": 0.132}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.372274, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.126, "width_percent": 0.134}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.37594, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.259, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.37979, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.398, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.383846, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.548, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.388039, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.697, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.391877, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.839, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.3959398, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.989, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.400002, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.14, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.4039629, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.287, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.4079258, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.43, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.411813, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.573, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.415751, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.716, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.419492, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.852, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.42368, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.004, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.427677, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.15, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.4316509, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.295, "width_percent": 0.136}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.435408, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.431, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.439285, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.572, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.443287, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.717, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.4473178, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.862, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.451305, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.007, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.455131, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.148, "width_percent": 0.134}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.458785, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.282, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.4627562, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.43, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.466516, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.57, "width_percent": 0.133}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.4700818, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.704, "width_percent": 0.13}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.4735558, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.833, "width_percent": 0.138}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.477244, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.971, "width_percent": 0.137}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.480976, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.108, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.485043, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.254, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.488908, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.398, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.492699, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.537, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.496554, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.679, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.5004132, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.822, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.504889, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.989, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.508929, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.14, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.512871, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.285, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.516907, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.432, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.520836, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.577, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.5249338, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.723, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.529453, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.875, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.534094, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.039, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.538498, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.195, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.543035, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.352, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.547559, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.513, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.5515778, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.661, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.5566, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.848, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.560852, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.001, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.565309, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.158, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.569479, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.313, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.5736468, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.463, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.5778801, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.615, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.582015, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.763, "width_percent": 0.158}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.586272, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.92, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.590381, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.068, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.5945508, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.219, "width_percent": 0.171}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.599339, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.39, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.603709, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.55, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.60784, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.7, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.6120799, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.85, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.616315, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.007, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.621018, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.179, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.625365, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.333, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.629659, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.488, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.63388, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.644, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.637869, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.788, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.64183, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.932, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.6463108, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.093, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.650429, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.244, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.654763, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.405, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.658953, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.558, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.663286, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.712, "width_percent": 0.165}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.667722, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.877, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.671694, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.02, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.675906, "duration": 0.00622, "duration_str": "6.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.168, "width_percent": 0.251}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.682499, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.418, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.686613, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.561, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.691188, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.715, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.695713, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.878, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.699847, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.028, "width_percent": 0.137}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7036312, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.165, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7078, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.311, "width_percent": 0.163}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7122982, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.474, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.71661, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.634, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.720673, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.783, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7247462, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.931, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.729336, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.097, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7332659, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.24, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.737535, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.391, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.741739, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.545, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.746211, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.701, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.750853, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.856, "width_percent": 0.21}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.756536, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.067, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.76053, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.213, "width_percent": 0.166}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.765154, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.378, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7693799, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.53, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.773462, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.674, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.777462, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.815, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.781483, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.962, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.785595, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.113, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7896602, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.263, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.793442, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.401, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.7981482, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.576, "width_percent": 0.152}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.802288, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.729, "width_percent": 0.157}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.8065588, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.885, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.810509, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.029, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.814603, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.171, "width_percent": 0.167}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.819096, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.338, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.82307, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.48, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.826955, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.62, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.830861, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.76, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.834894, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.908, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.838852, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.051, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.842672, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.192, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.8465059, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.332, "width_percent": 0.137}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.850205, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.469, "width_percent": 0.143}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.854094, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.612, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.857998, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.757, "width_percent": 0.138}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.861736, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.895, "width_percent": 0.134}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.865459, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.029, "width_percent": 0.133}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.8690138, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.162, "width_percent": 0.13}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.8725069, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.292, "width_percent": 0.137}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.8762028, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.429, "width_percent": 0.137}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.87988, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.566, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.8838859, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.715, "width_percent": 0.137}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.887549, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.852, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.891493, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.993, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.895529, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.139, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.899643, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.289, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.903496, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.431, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.907532, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.578, "width_percent": 0.141}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9113538, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.719, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9154391, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.871, "width_percent": 0.139}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9192932, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.01, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9233282, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.157, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.927422, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.304, "width_percent": 0.15}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.931602, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.453, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.93603, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.608, "width_percent": 0.142}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.939896, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.75, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9439352, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.897, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.948838, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.079, "width_percent": 0.147}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.952968, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.226, "width_percent": 0.144}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9570122, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.37, "width_percent": 0.181}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9619782, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.551, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.966204, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.704, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.970227, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.853, "width_percent": 0.145}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.974277, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.998, "width_percent": 0.161}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.97882, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.159, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.983113, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.314, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.987358, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.468, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.991548, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.623, "width_percent": 0.155}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952756.9959362, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.778, "width_percent": 0.153}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.000071, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.932, "width_percent": 0.14}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.0038679, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.071, "width_percent": 0.149}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.008001, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.22, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.0121632, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.374, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.016102, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.521, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.0203142, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.675, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.024414, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.821, "width_percent": 0.146}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.028372, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.967, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.03262, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.121, "width_percent": 0.151}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.03678, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.272, "width_percent": 0.148}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.0408459, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.42, "width_percent": 0.156}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.045204, "duration": 0.0068200000000000005, "duration_str": "6.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.576, "width_percent": 0.275}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752952757.05238, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.851, "width_percent": 0.149}, {"sql": "... 347 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Xmetr\\Page\\Models\\Page": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php:1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/theme/options", "action_name": "theme.options.post", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\ThemeController@postUpdate", "uri": "POST admin/theme/options/{id?}", "permission": "theme.options", "controller": "Xmetr\\Theme\\Http\\Controllers\\ThemeController@postUpdate<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FThemeController.php:88\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\Theme\\Http\\Controllers", "prefix": "admin/theme/options/{id?}", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FThemeController.php:88\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/ThemeController.php:88-111</a>", "middleware": "web, core, auth", "duration": "6.72s", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-543726924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-543726924\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-56200368 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IXGjHW0EqltPz3rfutxdkjYejhyX2AuH8ZqUYqdC</span>\"\n  \"<span class=sf-dump-key>ref_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>projects_list_page_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  \"<span class=sf-dump-key>properties_list_page_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n  \"<span class=sf-dump-key>number_of_projects_per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  \"<span class=sf-dump-key>number_of_properties_per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n  \"<span class=sf-dump-key>number_of_related_projects</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>number_of_related_properties</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>latitude_longitude_center_on_properties_page</span>\" => \"<span class=sf-dump-str title=\"21 characters\">43.615134, -76.393186</span>\"\n  \"<span class=sf-dump-key>real_estate_property_listing_layout</span>\" => \"<span class=sf-dump-str title=\"5 characters\">xmetr</span>\"\n  \"<span class=sf-dump-key>real_estate_property_detail_layout</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>real_estate_project_listing_layout</span>\" => \"<span class=sf-dump-str title=\"5 characters\">xmetr</span>\"\n  \"<span class=sf-dump-key>real_estate_show_map_on_single_detail_page</span>\" => \"<span class=sf-dump-str title=\"3 characters\">yes</span>\"\n  \"<span class=sf-dump-key>real_estate_use_location_in_search_box_as_dropdown</span>\" => \"<span class=sf-dump-str title=\"2 characters\">no</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56200368\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-527368427 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1851</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImE0WDM0UVpJWkFCOS9qR3ZPQ0s4MkE9PSIsInZhbHVlIjoiQkNOa0FRd2hBODc3dVo5ZVM5aEhrU3UwTVN0RmRUbzUrS3Q1bjFlZWpsMlEzbTB0d3cwR3NPK2IvN1JYaEtMZ1VTVmcyYXZXV0VuSnhaRWJpYUFIaW5uTnRoWEs1b2RWMFgvQjAyMWV3d2lXSHBSQVRsQnQvQ1BMTDNJd09WQmsiLCJtYWMiOiI1YzdjN2UzMTAwYjQ4Y2E2NTc3M2Y0MjdlN2YxN2MzMzVlZTZlMDAxMTU4YWFmZWEzNzViODgzNWI1YTdiNGI2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryPubPOLmd2nXMFy6W</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">https://xmetr.gc/admin/theme/options/opt-text-subsection-real-estate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; project_wishlist=25; wishlist=657; _hjSession_6417422=eyJpZCI6Ijg3Mjk5NzNiLTU1NjUtNDEyYi1hNTZlLTJlMTk4ODQzYTk2MCIsImMiOjE3NTI5NDgxNjUzNzUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1752948165$o95$g1$t1752952308$j44$l0$h0; XSRF-TOKEN=eyJpdiI6ImE0WDM0UVpJWkFCOS9qR3ZPQ0s4MkE9PSIsInZhbHVlIjoiQkNOa0FRd2hBODc3dVo5ZVM5aEhrU3UwTVN0RmRUbzUrS3Q1bjFlZWpsMlEzbTB0d3cwR3NPK2IvN1JYaEtMZ1VTVmcyYXZXV0VuSnhaRWJpYUFIaW5uTnRoWEs1b2RWMFgvQjAyMWV3d2lXSHBSQVRsQnQvQ1BMTDNJd09WQmsiLCJtYWMiOiI1YzdjN2UzMTAwYjQ4Y2E2NTc3M2Y0MjdlN2YxN2MzMzVlZTZlMDAxMTU4YWFmZWEzNzViODgzNWI1YTdiNGI2IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6ImY5YjhGTm9RZk1yNGFHZ3hyQ0dBSlE9PSIsInZhbHVlIjoiUXNjOUVXT01HOENTek5iNWlVSWN5OXZLQTh1K2hNb2cyeXBNRTFrSXNDTEplbnBQZ3pod1UzTWRVd2ZyU1NyYStPOXhjVnRrU28zcGltSmZDeC9WNXVHa3JuSEcwb2Vka2N3dUUvNDdDMzlMSGpWZkxCRkd1aGFDSDN5V0FGVngiLCJtYWMiOiI0OWQwZTIyYzFhZjU4MTdjOTk3MjQ1ODA3NGJlNzk2NjVmNzZmZTM4NzA2MGZmNzMwYTRmYWEyMThlMzVlOTUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527368427\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-42613606 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IXGjHW0EqltPz3rfutxdkjYejhyX2AuH8ZqUYqdC</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ao5wIXd5JG8FpH7TPxAaB6WV42m94erVNEyHw9Z4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-42613606\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-573081773 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 19:19:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573081773\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IXGjHW0EqltPz3rfutxdkjYejhyX2AuH8ZqUYqdC</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"68 characters\">https://xmetr.gc/admin/theme/options/opt-text-subsection-real-estate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>101</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>101</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/theme/options", "action_name": "theme.options.post", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\ThemeController@postUpdate"}, "badge": null}}