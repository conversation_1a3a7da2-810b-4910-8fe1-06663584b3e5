<?php
    $categories = get_property_categories([
        'indent' => '↳',
        'conditions' => ['status' => \Xmetr\Base\Enums\BaseStatusEnum::PUBLISHED],
    ]);
?>

<div class="form-group-3 form-style">
    <label for="category"><?php echo e(__('Category')); ?></label>
    <div class="group-select">
        <select name="category" id="category" class="select_js">
            <option value=""><?php echo e(__('All')); ?></option>
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($category->slugable->key); ?>"<?php if(request()->query('category') == $category->slugable->key): echo 'selected'; endif; ?>><?php echo e($category->name); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
    </div>
</div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/filters/categories.blade.php ENDPATH**/ ?>