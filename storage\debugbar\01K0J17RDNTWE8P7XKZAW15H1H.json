{"__meta": {"id": "01K0J17RDNTWE8P7XKZAW15H1H", "datetime": "2025-07-19 19:05:39", "utime": **********.510914, "method": "POST", "uri": "/admin/real-estate/properties/edit/1169", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[19:05:38] LOG.error: Value \"\" is not part of the enum Xmetr\\RealEstate\\Enums\\RentalPeriodEnum", "message_html": null, "is_string": false, "label": "error", "time": **********.130928, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752951936.411198, "end": **********.510963, "duration": 3.0997650623321533, "duration_str": "3.1s", "measures": [{"label": "Booting", "start": 1752951936.411198, "relative_start": 0, "end": **********.605421, "relative_end": **********.605421, "duration": 1.***************, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.605445, "relative_start": 1.****************, "end": **********.510969, "relative_end": 5.9604644775390625e-06, "duration": 1.****************, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.641997, "relative_start": 1.****************, "end": **********.670458, "relative_end": **********.670458, "duration": 0.028460979461669922, "duration_str": "28.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/real-estate::partials.form-features", "start": **********.14559, "relative_start": 1.****************, "end": **********.14559, "relative_end": **********.14559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.15593, "relative_start": 1.***************, "end": **********.15593, "relative_end": **********.15593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.161007, "relative_start": 1.7498090267181396, "end": **********.161007, "relative_end": **********.161007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.164619, "relative_start": 1.7534210681915283, "end": **********.164619, "relative_end": **********.164619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.168721, "relative_start": 1.7575230598449707, "end": **********.168721, "relative_end": **********.168721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.173311, "relative_start": 1.762113094329834, "end": **********.173311, "relative_end": **********.173311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.177947, "relative_start": 1.7667491436004639, "end": **********.177947, "relative_end": **********.177947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.182181, "relative_start": 1.7709829807281494, "end": **********.182181, "relative_end": **********.182181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.186427, "relative_start": 1.7752292156219482, "end": **********.186427, "relative_end": **********.186427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.191939, "relative_start": 1.7807412147521973, "end": **********.191939, "relative_end": **********.191939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.196795, "relative_start": 1.7855970859527588, "end": **********.196795, "relative_end": **********.196795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.199942, "relative_start": 1.7887442111968994, "end": **********.199942, "relative_end": **********.199942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.203013, "relative_start": 1.7918150424957275, "end": **********.203013, "relative_end": **********.203013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.20753, "relative_start": 1.7963321208953857, "end": **********.20753, "relative_end": **********.20753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.214107, "relative_start": 1.8029091358184814, "end": **********.214107, "relative_end": **********.214107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.220348, "relative_start": 1.809149980545044, "end": **********.220348, "relative_end": **********.220348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.226557, "relative_start": 1.815359115600586, "end": **********.226557, "relative_end": **********.226557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.230214, "relative_start": 1.8190162181854248, "end": **********.230214, "relative_end": **********.230214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.235519, "relative_start": 1.8243210315704346, "end": **********.235519, "relative_end": **********.235519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.24096, "relative_start": 1.8297619819641113, "end": **********.24096, "relative_end": **********.24096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.245766, "relative_start": 1.8345680236816406, "end": **********.245766, "relative_end": **********.245766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.250037, "relative_start": 1.838839054107666, "end": **********.250037, "relative_end": **********.250037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::partials.form-suitable", "start": **********.252717, "relative_start": 1.8415191173553467, "end": **********.252717, "relative_end": **********.252717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.254002, "relative_start": 1.842804193496704, "end": **********.254002, "relative_end": **********.254002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.256408, "relative_start": 1.845210075378418, "end": **********.256408, "relative_end": **********.256408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.258273, "relative_start": 1.8470749855041504, "end": **********.258273, "relative_end": **********.258273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::partials.moderation-status", "start": **********.267345, "relative_start": 1.856147050857544, "end": **********.267345, "relative_end": **********.267345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.badge", "start": **********.273989, "relative_start": 1.8627910614013672, "end": **********.273989, "relative_end": **********.273989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.436263, "relative_start": 3.0250651836395264, "end": **********.505174, "relative_end": **********.505174, "duration": 0.06891083717346191, "duration_str": "68.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 47312640, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 28, "nb_templates": 28, "templates": [{"name": "plugins/real-estate::partials.form-features", "param_count": null, "params": [], "start": **********.145502, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-features.blade.phpplugins/real-estate::partials.form-features", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-features.blade.php:1", "ajax": false, "filename": "form-features.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.155867, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.16097, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.164562, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.168662, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.173255, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.17789, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.182129, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.186366, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.191865, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.196729, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.1999, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.202958, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.207445, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.213998, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.220281, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.226475, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.230178, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.235448, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.240884, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.24572, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.249913, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "plugins/real-estate::partials.form-suitable", "param_count": null, "params": [], "start": **********.252663, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-suitable.blade.phpplugins/real-estate::partials.form-suitable", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-suitable.blade.php:1", "ajax": false, "filename": "form-suitable.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.25383, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.256361, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.258221, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "plugins/real-estate::partials.moderation-status", "param_count": null, "params": [], "start": **********.267293, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/moderation-status.blade.phpplugins/real-estate::partials.moderation-status", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fmoderation-status.blade.php:1", "ajax": false, "filename": "moderation-status.blade.php", "line": "?"}}, {"name": "core/base::components.badge", "param_count": null, "params": [], "start": **********.273929, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/badge.blade.phpcore/base::components.badge", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php:1", "ajax": false, "filename": "badge.blade.php", "line": "?"}}]}, "queries": {"count": 31, "nb_statements": 31, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.21662, "accumulated_duration_str": "217ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.691652, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 1.126}, {"sql": "select * from `re_properties` where `re_properties`.`id` = '1169' limit 1", "type": "query", "params": [], "bindings": ["1169"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 127}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9012601, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.126, "width_percent": 0.937}, {"sql": "select `name`, `id` from `re_projects` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 71}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.909318, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 2.064, "width_percent": 0.42}, {"sql": "select `title`, `id` from `re_currencies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.922831, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:75", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:75", "ajax": false, "filename": "PropertyForm.php", "line": "75"}, "connection": "xmetr", "explain": null, "start_percent": 2.484, "width_percent": 0.392}, {"sql": "select `category_id` from `re_categories` inner join `re_property_categories` on `re_categories`.`id` = `re_property_categories`.`category_id` where `re_property_categories`.`property_id` = 1169", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 84}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.96417, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:84", "source": {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 84}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:84", "ajax": false, "filename": "PropertyForm.php", "line": "84"}, "connection": "xmetr", "explain": null, "start_percent": 2.876, "width_percent": 0.411}, {"sql": "select `id` from `re_features` inner join `re_property_features` on `re_features`.`id` = `re_property_features`.`feature_id` where `re_property_features`.`property_id` = 1169", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 94}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.968405, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:94", "source": {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 94}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:94", "ajax": false, "filename": "PropertyForm.php", "line": "94"}, "connection": "xmetr", "explain": null, "start_percent": 3.287, "width_percent": 1.787}, {"sql": "select `id`, `name` from `re_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 99}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.975708, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.073, "width_percent": 0.3}, {"sql": "select `id`, `name` from `re_facilities`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 123}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 18, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.009186, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.373, "width_percent": 0.402}, {"sql": "select `re_facilities`.`id`, `distance`, `re_facilities_distances`.`reference_id` as `pivot_reference_id`, `re_facilities_distances`.`facility_id` as `pivot_facility_id`, `re_facilities_distances`.`reference_type` as `pivot_reference_type`, `re_facilities_distances`.`distance` as `pivot_distance` from `re_facilities` inner join `re_facilities_distances` on `re_facilities`.`id` = `re_facilities_distances`.`facility_id` where `re_facilities_distances`.`reference_id` = 1169 and `re_facilities_distances`.`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": [1169, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 134}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 17, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.0607688, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:134", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 134}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:134", "ajax": false, "filename": "PropertyForm.php", "line": "134"}, "connection": "xmetr", "explain": null, "start_percent": 5.775, "width_percent": 0.42}, {"sql": "select `id`, `name`, `parent_id` from `re_categories` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 126}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 38}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 20, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.282913, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.195, "width_percent": 0.351}, {"sql": "select * from `re_categories_translations` where `re_categories_translations`.`re_categories_id` in (4, 7, 8) and `re_categories_translations`.`lang_code` = 'en_US'", "type": "query", "params": [], "bindings": ["en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 126}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 38}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}], "start": **********.291043, "duration": 0.020390000000000002, "duration_str": "20.39ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.546, "width_percent": 9.413}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 621}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 620}], "start": **********.333157, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 15.959, "width_percent": 0.614}, {"sql": "select * from `cities` where `cities`.`id` = 8212 limit 1", "type": "query", "params": [], "bindings": [8212], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 621}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 620}, {"index": 26, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}], "start": **********.339881, "duration": 0.019969999999999998, "duration_str": "19.97ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 16.573, "width_percent": 9.219}, {"sql": "update `re_properties` set `type` = 'rent', `project_id` = '25', `period` = 'month', `rental_period` = '', `status` = 'renting', `moderation_status` = 'approved', `re_properties`.`updated_at` = '2025-07-19 19:05:38' where `id` = 1169", "type": "query", "params": [], "bindings": [{"value": "rent", "label": "Rent"}, "25", {"value": "month", "label": "Monthly"}, {"value": null, "label": ""}, {"value": "renting", "label": "Renting"}, {"value": "approved", "label": "Approved"}, "2025-07-19 19:05:38", 1169], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 149}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.451843, "duration": 0.0062900000000000005, "duration_str": "6.29ms", "memory": 0, "memory_str": null, "filename": "PropertyController.php:149", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 149}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:149", "ajax": false, "filename": "PropertyController.php", "line": "149"}, "connection": "xmetr", "explain": null, "start_percent": 25.792, "width_percent": 2.904}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1169 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1169, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.604191, "duration": 0.01109, "duration_str": "11.09ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 28.695, "width_percent": 5.12}, {"sql": "select * from `slugs` where (`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `reference_id` = 1169) limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1169], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 24, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 151}], "start": **********.675032, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 33.815, "width_percent": 2.22}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 889}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\UpdatedContentListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.854805, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 36.035, "width_percent": 0.572}, {"sql": "delete from `audit_histories` where `created_at` < '2025-06-19 19:05:39' limit 1000", "type": "query", "params": [], "bindings": ["2025-06-19 19:05:39"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 151}], "start": **********.045086, "duration": 0.02106, "duration_str": "21.06ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:55", "source": {"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:55", "ajax": false, "filename": "AuditHandlerListener.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 36.608, "width_percent": 9.722}, {"sql": "delete from `audit_histories` where `created_at` < '2025-06-19 19:05:39' limit 1000", "type": "query", "params": [], "bindings": ["2025-06-19 19:05:39"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 151}], "start": **********.1222131, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:55", "source": {"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:55", "ajax": false, "filename": "AuditHandlerListener.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 46.33, "width_percent": 1.44}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'property', 'updated', 1, 1, 1169, 'Long-term rent of a 2-room apartment 120m² in central Burgas, Bulgaria', 'primary', '2025-07-19 19:05:39', '2025-07-19 19:05:39', '{\\\"name\\\":\\\"Long-term rent of a 2-room apartment 120m\\\\u00b2 in central Burgas, Bulgaria\\\",\\\"model\\\":\\\"Xmetr\\\\\\\\RealEstate\\\\\\\\Models\\\\\\\\Property\\\",\\\"slug\\\":\\\"long-term-rent-of-a-2-room-apartment-120m2-in-central-burgas-bulgaria\\\",\\\"slug_id\\\":\\\"2882\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"description\\\":null,\\\"original_description\\\":\\\"\\\\ud83c\\\\udfe0 1200 \\\\u041b\\\\u0415\\\\u0412\\\\u0410, \\\\u0421\\\\u0443\\\\u043f\\\\u0435\\\\u0440 \\\\u0426\\\\u0435\\\\u043d\\\\u0442\\\\u0440, \\\\u0443\\\\u043b. \\\\u0411\\\\u043e\\\\u0433\\\\u043e\\\\u0440\\\\u0438\\\\u0434\\\\u0438,\\\\r\\\\n\\\\r\\\\n\\\\u0414\\\\u0432\\\\u0443\\\\u0445\\\\u043a\\\\u043e\\\\u043c\\\\u043d\\\\u0430\\\\u0442\\\\u043d\\\\u0430\\\\u044f \\\\u043a\\\\u0432\\\\u0430\\\\u0440\\\\u0442\\\\u0438\\\\u0440\\\\u0430, 120\\\\u043c2, 2 \\\\u0441\\\\u0440\\\\u0435\\\\u0434\\\\u043d\\\\u0438\\\\u0439 \\\\u044d\\\\u0442\\\\u0430\\\\u0436, \\\\u0431\\\\u0435\\\\u0437 \\\\u043b\\\\u0438\\\\u0444\\\\u0442\\\\u0430, \\\\u041016;\\\\r\\\\n\\\\r\\\\n\\\\u0421\\\\u043e\\\\u0441\\\\u0442\\\\u043e\\\\u0438\\\\u0442 \\\\u0438\\\\u0437 \\\\u043a\\\\u043e\\\\u0440\\\\u0438\\\\u0434\\\\u043e\\\\u0440\\\\u0430, \\\\u043a\\\\u0443\\\\u0445\\\\u043d\\\\u0438 \\\\u0441 \\\\u0433\\\\u043e\\\\u0441\\\\u0442\\\\u0438\\\\u043d\\\\u043e\\\\u0439, \\\\u0441\\\\u043f\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u0438, \\\\u0432\\\\u0430\\\\u043d\\\\u043d\\\\u043e\\\\u0439 \\\\u043a\\\\u043e\\\\u043c\\\\u043d\\\\u0430\\\\u0442\\\\u044b \\\\u0438 \\\\u0442\\\\u0435\\\\u0440\\\\u0440\\\\u0430\\\\u0441\\\\u044b.\\\\r\\\\n\\\\r\\\\n\\\\ud83d\\\\udc9b \\\\ud83d\\\\udc9b \\\\u0415\\\\u0441\\\\u0442\\\\u044c \\\\u0441\\\\u0442\\\\u0438\\\\u0440\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u0430\\\\u044f, \\\\u043f\\\\u043e\\\\u0441\\\\u0443\\\\u0434\\\\u043e\\\\u043c\\\\u043e\\\\u0435\\\\u0447\\\\u043d\\\\u0430\\\\u044f \\\\u043c\\\\u0430\\\\u0448\\\\u0438\\\\u043d\\\\u0430, \\\\u0434\\\\u0443\\\\u0445\\\\u043e\\\\u0432\\\\u043e\\\\u0439 \\\\u0448\\\\u043a\\\\u0430\\\\u0444, \\\\u0432\\\\u0430\\\\u0440\\\\u043e\\\\u0447\\\\u043d\\\\u0430\\\\u044f \\\\u043f\\\\u0430\\\\u043d\\\\u0435\\\\u043b\\\\u044c, \\\\u0447\\\\u0430\\\\u0439\\\\u043d\\\\u0438\\\\u043a, \\\\u0442\\\\u0432, \\\\u043a\\\\u043e\\\\u043d\\\\u0434\\\\u0438\\\\u0446\\\\u0438\\\\u043e\\\\u043d\\\\u0435\\\\u0440\\\\u044b.\\\\r\\\\n\\\\u2795 \\\\u0415\\\\u0441\\\\u0442\\\\u044c \\\\u043f\\\\u043e\\\\u0441\\\\u0443\\\\u0434\\\\u0430 \\\\u0438 \\\\u0431\\\\u0435\\\\u043b\\\\u044c\\\\u0435. \\\\r\\\\n\\\\r\\\\n\\\\ud83d\\\\udccc \\\\u041d\\\\u0430\\\\u0445\\\\u043e\\\\u0434\\\\u0438\\\\u0442\\\\u0441\\\\u044f \\\\u043d\\\\u0430 \\\\u0446\\\\u0435\\\\u043d\\\\u0442\\\\u0440\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u043e\\\\u0439 \\\\u043f\\\\u0435\\\\u0448\\\\u0435\\\\u0445\\\\u043e\\\\u0434\\\\u043d\\\\u043e\\\\u0439 \\\\u0443\\\\u043b\\\\u0438\\\\u0446\\\\u0435 \\\\u0411\\\\u043e\\\\u0433\\\\u043e\\\\u0440\\\\u0438\\\\u0434\\\\u0438. \\\\u0426\\\\u0435\\\\u043d\\\\u0442\\\\u0440\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u0435\\\\u0435 \\\\u0443\\\\u0436\\\\u0435 \\\\u043d\\\\u0435\\\\u043a\\\\u0443\\\\u0434\\\\u0430. 5 \\\\u043c\\\\u0438\\\\u043d\\\\u0443\\\\u0442 \\\\u043f\\\\u0435\\\\u0448\\\\u043a\\\\u043e\\\\u043c \\\\u0434\\\\u043e \\\\u043f\\\\u043b\\\\u044f\\\\u0436\\\\u0430. \\\\u0412\\\\u043e\\\\u043a\\\\u0440\\\\u0443\\\\u0433 \\\\u043c\\\\u0430\\\\u0433\\\\u0430\\\\u0437\\\\u0438\\\\u043d\\\\u044b \\\\u0440\\\\u0435\\\\u0441\\\\u0442\\\\u043e\\\\u0440\\\\u0430\\\\u043d\\\\u044b, \\\\u0431\\\\u0430\\\\u0440\\\\u044b, \\\\u043f\\\\u043e\\\\u0440\\\\u0442, \\\\u0434\\\\u0435\\\\u0442\\\\u0441\\\\u043a\\\\u0430\\\\u044f \\\\u043f\\\\u043b\\\\u043e\\\\u0449\\\\u0430\\\\u0434\\\\u043a\\\\u0430.\\\\r\\\\n\\\\r\\\\n\\\\u2139\\\\ufe0f \\\\u0421\\\\u0434\\\\u0430\\\\u0435\\\\u0442\\\\u0441\\\\u044f \\\\u0434\\\\u043e\\\\u043b\\\\u0433\\\\u043e\\\\u0441\\\\u0440\\\\u043e\\\\u0447\\\\u043d\\\\u043e\\\\r\\\\n\\\\u23f3 \\\\u041e\\\\u0441\\\\u0432\\\\u043e\\\\u0431\\\\u043e\\\\u0436\\\\u0434\\\\u0430\\\\u0435\\\\u0442\\\\u0441\\\\u044f 01\\\\/09\\\\r\\\\n\\\\u041e\\\\u0441\\\\u0435\\\\u043d\\\\u044c.\\\",\\\"is_featured\\\":\\\"0\\\",\\\"content\\\":\\\"<p>\\\\ud83c\\\\udfe0 1200 LEVA, Super Center, Bogoridi street,<\\\\/p><p>Two-room apartment, 120m2, 2nd middle floor, no elevator, A16;<\\\\/p><p>Consists of a corridor, kitchen with living room, bedroom, bathroom and terrace.<\\\\/p><p>\\\\ud83d\\\\udc9b \\\\ud83d\\\\udc9b There is a washing machine, dishwasher, oven, hob, kettle, TV, air conditioners.<br>\\\\u2795 There are dishes and linen.<\\\\/p><p>\\\\ud83d\\\\udccc It is located on the central pedestrian street of Bogoridi. There is nowhere more central. 5 minutes walk to the beach. Around shops, restaurants, bars, port, playground.<\\\\/p><p>\\\\u2139\\\\ufe0f Long-term rent<br>\\\\u23f3 Available 01\\\\/09<br>Autumn.<\\\\/p>\\\",\\\"images\\\":[null,\\\".\\\\/photo-2025-06-19-204222.webp\\\",\\\".\\\\/photo-2025-06-19-204226.webp\\\",\\\".\\\\/photo-2025-06-19-204225.webp\\\",\\\".\\\\/photo-2025-06-19-204224.webp\\\",\\\".\\\\/photo-2025-06-19-204227.webp\\\",\\\".\\\\/photo-2025-06-19-204223.webp\\\",\\\".\\\\/photo-2025-06-19-204221.webp\\\"],\\\"video\\\":null,\\\"country_id\\\":\\\"34\\\",\\\"state_id\\\":null,\\\"city_id\\\":\\\"8212\\\",\\\"district_id\\\":null,\\\"location\\\":\\\"Burgas, Bulgaria\\\",\\\"latitude\\\":\\\"42.50479259999999\\\",\\\"longitude\\\":\\\"27.4626361\\\",\\\"number_bedroom\\\":null,\\\"number_bathroom\\\":null,\\\"number_floor\\\":\\\"2\\\",\\\"square\\\":\\\"120\\\",\\\"price\\\":\\\"1200\\\",\\\"currency_id\\\":\\\"10\\\",\\\"commission\\\":null,\\\"deposit\\\":null,\\\"never_expired\\\":\\\"1\\\",\\\"bills_included\\\":\\\"0\\\",\\\"utilities\\\":null,\\\"furnished\\\":\\\"1\\\",\\\"pets_allowed\\\":\\\"0\\\",\\\"smoking_allowed\\\":\\\"0\\\",\\\"online_view_tour\\\":\\\"0\\\",\\\"features\\\":[\\\"3\\\",\\\"7\\\",\\\"13\\\",\\\"14\\\",\\\"16\\\"],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"apply\\\",\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"renting\\\",\\\"categories\\\":[\\\"7\\\"],\\\"unique_id\\\":null,\\\"project_id\\\":\\\"25\\\",\\\"author_id\\\":\\\"74\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "property", "updated", 1, 1, 1169, "Long-term rent of a 2-room apartment 120m² in central Burgas, Bulgaria", "primary", "2025-07-19 19:05:39", "2025-07-19 19:05:39", "{\"name\":\"Long-term rent of a 2-room apartment 120m\\u00b2 in central Burgas, Bulgaria\",\"model\":\"Xmetr\\\\RealEstate\\\\Models\\\\Property\",\"slug\":\"long-term-rent-of-a-2-room-apartment-120m2-in-central-burgas-bulgaria\",\"slug_id\":\"2882\",\"is_slug_editable\":\"1\",\"description\":null,\"original_description\":\"\\ud83c\\udfe0 1200 \\u041b\\u0415\\u0412\\u0410, \\u0421\\u0443\\u043f\\u0435\\u0440 \\u0426\\u0435\\u043d\\u0442\\u0440, \\u0443\\u043b. \\u0411\\u043e\\u0433\\u043e\\u0440\\u0438\\u0434\\u0438,\\r\\n\\r\\n\\u0414\\u0432\\u0443\\u0445\\u043a\\u043e\\u043c\\u043d\\u0430\\u0442\\u043d\\u0430\\u044f \\u043a\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430, 120\\u043c2, 2 \\u0441\\u0440\\u0435\\u0434\\u043d\\u0438\\u0439 \\u044d\\u0442\\u0430\\u0436, \\u0431\\u0435\\u0437 \\u043b\\u0438\\u0444\\u0442\\u0430, \\u041016;\\r\\n\\r\\n\\u0421\\u043e\\u0441\\u0442\\u043e\\u0438\\u0442 \\u0438\\u0437 \\u043a\\u043e\\u0440\\u0438\\u0434\\u043e\\u0440\\u0430, \\u043a\\u0443\\u0445\\u043d\\u0438 \\u0441 \\u0433\\u043e\\u0441\\u0442\\u0438\\u043d\\u043e\\u0439, \\u0441\\u043f\\u0430\\u043b\\u044c\\u043d\\u0438, \\u0432\\u0430\\u043d\\u043d\\u043e\\u0439 \\u043a\\u043e\\u043c\\u043d\\u0430\\u0442\\u044b \\u0438 \\u0442\\u0435\\u0440\\u0440\\u0430\\u0441\\u044b.\\r\\n\\r\\n\\ud83d\\udc9b \\ud83d\\udc9b \\u0415\\u0441\\u0442\\u044c \\u0441\\u0442\\u0438\\u0440\\u0430\\u043b\\u044c\\u043d\\u0430\\u044f, \\u043f\\u043e\\u0441\\u0443\\u0434\\u043e\\u043c\\u043e\\u0435\\u0447\\u043d\\u0430\\u044f \\u043c\\u0430\\u0448\\u0438\\u043d\\u0430, \\u0434\\u0443\\u0445\\u043e\\u0432\\u043e\\u0439 \\u0448\\u043a\\u0430\\u0444, \\u0432\\u0430\\u0440\\u043e\\u0447\\u043d\\u0430\\u044f \\u043f\\u0430\\u043d\\u0435\\u043b\\u044c, \\u0447\\u0430\\u0439\\u043d\\u0438\\u043a, \\u0442\\u0432, \\u043a\\u043e\\u043d\\u0434\\u0438\\u0446\\u0438\\u043e\\u043d\\u0435\\u0440\\u044b.\\r\\n\\u2795 \\u0415\\u0441\\u0442\\u044c \\u043f\\u043e\\u0441\\u0443\\u0434\\u0430 \\u0438 \\u0431\\u0435\\u043b\\u044c\\u0435. \\r\\n\\r\\n\\ud83d\\udccc \\u041d\\u0430\\u0445\\u043e\\u0434\\u0438\\u0442\\u0441\\u044f \\u043d\\u0430 \\u0446\\u0435\\u043d\\u0442\\u0440\\u0430\\u043b\\u044c\\u043d\\u043e\\u0439 \\u043f\\u0435\\u0448\\u0435\\u0445\\u043e\\u0434\\u043d\\u043e\\u0439 \\u0443\\u043b\\u0438\\u0446\\u0435 \\u0411\\u043e\\u0433\\u043e\\u0440\\u0438\\u0434\\u0438. \\u0426\\u0435\\u043d\\u0442\\u0440\\u0430\\u043b\\u044c\\u043d\\u0435\\u0435 \\u0443\\u0436\\u0435 \\u043d\\u0435\\u043a\\u0443\\u0434\\u0430. 5 \\u043c\\u0438\\u043d\\u0443\\u0442 \\u043f\\u0435\\u0448\\u043a\\u043e\\u043c \\u0434\\u043e \\u043f\\u043b\\u044f\\u0436\\u0430. \\u0412\\u043e\\u043a\\u0440\\u0443\\u0433 \\u043c\\u0430\\u0433\\u0430\\u0437\\u0438\\u043d\\u044b \\u0440\\u0435\\u0441\\u0442\\u043e\\u0440\\u0430\\u043d\\u044b, \\u0431\\u0430\\u0440\\u044b, \\u043f\\u043e\\u0440\\u0442, \\u0434\\u0435\\u0442\\u0441\\u043a\\u0430\\u044f \\u043f\\u043b\\u043e\\u0449\\u0430\\u0434\\u043a\\u0430.\\r\\n\\r\\n\\u2139\\ufe0f \\u0421\\u0434\\u0430\\u0435\\u0442\\u0441\\u044f \\u0434\\u043e\\u043b\\u0433\\u043e\\u0441\\u0440\\u043e\\u0447\\u043d\\u043e\\r\\n\\u23f3 \\u041e\\u0441\\u0432\\u043e\\u0431\\u043e\\u0436\\u0434\\u0430\\u0435\\u0442\\u0441\\u044f 01\\/09\\r\\n\\u041e\\u0441\\u0435\\u043d\\u044c.\",\"is_featured\":\"0\",\"content\":\"<p>\\ud83c\\udfe0 1200 LEVA, Super Center, Bogoridi street,<\\/p><p>Two-room apartment, 120m2, 2nd middle floor, no elevator, A16;<\\/p><p>Consists of a corridor, kitchen with living room, bedroom, bathroom and terrace.<\\/p><p>\\ud83d\\udc9b \\ud83d\\udc9b There is a washing machine, dishwasher, oven, hob, kettle, TV, air conditioners.<br>\\u2795 There are dishes and linen.<\\/p><p>\\ud83d\\udccc It is located on the central pedestrian street of Bogoridi. There is nowhere more central. 5 minutes walk to the beach. Around shops, restaurants, bars, port, playground.<\\/p><p>\\u2139\\ufe0f Long-term rent<br>\\u23f3 Available 01\\/09<br>Autumn.<\\/p>\",\"images\":[null,\".\\/photo-2025-06-19-204222.webp\",\".\\/photo-2025-06-19-204226.webp\",\".\\/photo-2025-06-19-204225.webp\",\".\\/photo-2025-06-19-204224.webp\",\".\\/photo-2025-06-19-204227.webp\",\".\\/photo-2025-06-19-204223.webp\",\".\\/photo-2025-06-19-204221.webp\"],\"video\":null,\"country_id\":\"34\",\"state_id\":null,\"city_id\":\"8212\",\"district_id\":null,\"location\":\"Burgas, Bulgaria\",\"latitude\":\"42.50479259999999\",\"longitude\":\"27.4626361\",\"number_bedroom\":null,\"number_bathroom\":null,\"number_floor\":\"2\",\"square\":\"120\",\"price\":\"1200\",\"currency_id\":\"10\",\"commission\":null,\"deposit\":null,\"never_expired\":\"1\",\"bills_included\":\"0\",\"utilities\":null,\"furnished\":\"1\",\"pets_allowed\":\"0\",\"smoking_allowed\":\"0\",\"online_view_tour\":\"0\",\"features\":[\"3\",\"7\",\"13\",\"14\",\"16\"],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"apply\",\"language\":\"en_US\",\"status\":\"renting\",\"categories\":[\"7\"],\"unique_id\":null,\"project_id\":\"25\",\"author_id\":\"74\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 151}], "start": **********.129945, "duration": 0.07442, "duration_str": "74.42ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 47.77, "width_percent": 34.355}, {"sql": "select * from `re_property_features` where `re_property_features`.`property_id` = 1169", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 157}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.30917, "duration": 0.008150000000000001, "duration_str": "8.15ms", "memory": 0, "memory_str": null, "filename": "PropertyController.php:157", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 157}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:157", "ajax": false, "filename": "PropertyController.php", "line": "157"}, "connection": "xmetr", "explain": null, "start_percent": 82.125, "width_percent": 3.762}, {"sql": "update `re_properties` set `suitable_for` = '[]', `re_properties`.`updated_at` = '2025-07-19 19:05:39' where `id` = 1169", "type": "query", "params": [], "bindings": ["[]", "2025-07-19 19:05:39", 1169], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 161}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3287458, "duration": 0.00724, "duration_str": "7.24ms", "memory": 0, "memory_str": null, "filename": "PropertyController.php:161", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 161}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:161", "ajax": false, "filename": "PropertyController.php", "line": "161"}, "connection": "xmetr", "explain": null, "start_percent": 85.888, "width_percent": 3.342}, {"sql": "delete from `re_facilities_distances` where `re_facilities_distances`.`reference_id` = 1169 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": [1169, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 163}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.342893, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "SaveFacilitiesService.php:12", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FSaveFacilitiesService.php:12", "ajax": false, "filename": "SaveFacilitiesService.php", "line": "12"}, "connection": "xmetr", "explain": null, "start_percent": 89.23, "width_percent": 0.503}, {"sql": "select * from `re_property_categories` where `re_property_categories`.`property_id` = 1169", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StorePropertyCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StorePropertyCategoryService.php", "line": 16}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 164}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.349175, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "StorePropertyCategoryService.php:16", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StorePropertyCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StorePropertyCategoryService.php", "line": 16}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FStorePropertyCategoryService.php:16", "ajax": false, "filename": "StorePropertyCategoryService.php", "line": "16"}, "connection": "xmetr", "explain": null, "start_percent": 89.733, "width_percent": 0.503}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'status' and `reference_id` = 1169 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["status", 1169, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 63}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Traits/Forms/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Traits\\Forms\\HasMetadata.php", "line": 71}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 498}], "start": **********.354844, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 90.236, "width_percent": 1.45}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'commission' and `reference_id` = 1169 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["commission", 1169, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 65}, {"index": 15, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 898}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}], "start": **********.366473, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 91.686, "width_percent": 0.872}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'deposit' and `reference_id` = 1169 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["deposit", 1169, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 65}, {"index": 15, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 899}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}], "start": **********.373895, "duration": 0.006849999999999999, "duration_str": "6.85ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 92.558, "width_percent": 3.162}, {"sql": "update `re_properties` set `type` = 'rent', `period` = 'month', `re_properties`.`updated_at` = '2025-07-19 19:05:39' where `id` = 1169", "type": "query", "params": [], "bindings": [{"value": "rent", "label": "Rent"}, {"value": "month", "label": "Monthly"}, "2025-07-19 19:05:39", 1169], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 902}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/PropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\PropertyController.php", "line": 131}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.394368, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "functions.php:902", "source": {"index": 18, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 902}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Ffunctions%2Ffunctions.php:902", "ajax": false, "filename": "functions.php", "line": "902"}, "connection": "xmetr", "explain": null, "start_percent": 95.721, "width_percent": 0.591}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1169 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1169, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.404534, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.312, "width_percent": 0.388}, {"sql": "select * from `slugs` where (`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property' and `reference_id` = 1169) limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Property", 1169], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": **********.412252, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.699, "width_percent": 0.36}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'property', 'updated', 1, 1, 1169, 'Long-term rent of a 2-room apartment 120m² in central Burgas, Bulgaria', 'primary', '2025-07-19 19:05:39', '2025-07-19 19:05:39', '{\\\"name\\\":\\\"Long-term rent of a 2-room apartment 120m\\\\u00b2 in central Burgas, Bulgaria\\\",\\\"model\\\":\\\"Xmetr\\\\\\\\RealEstate\\\\\\\\Models\\\\\\\\Property\\\",\\\"slug\\\":\\\"long-term-rent-of-a-2-room-apartment-120m2-in-central-burgas-bulgaria\\\",\\\"slug_id\\\":\\\"2882\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"description\\\":null,\\\"original_description\\\":\\\"\\\\ud83c\\\\udfe0 1200 \\\\u041b\\\\u0415\\\\u0412\\\\u0410, \\\\u0421\\\\u0443\\\\u043f\\\\u0435\\\\u0440 \\\\u0426\\\\u0435\\\\u043d\\\\u0442\\\\u0440, \\\\u0443\\\\u043b. \\\\u0411\\\\u043e\\\\u0433\\\\u043e\\\\u0440\\\\u0438\\\\u0434\\\\u0438,\\\\r\\\\n\\\\r\\\\n\\\\u0414\\\\u0432\\\\u0443\\\\u0445\\\\u043a\\\\u043e\\\\u043c\\\\u043d\\\\u0430\\\\u0442\\\\u043d\\\\u0430\\\\u044f \\\\u043a\\\\u0432\\\\u0430\\\\u0440\\\\u0442\\\\u0438\\\\u0440\\\\u0430, 120\\\\u043c2, 2 \\\\u0441\\\\u0440\\\\u0435\\\\u0434\\\\u043d\\\\u0438\\\\u0439 \\\\u044d\\\\u0442\\\\u0430\\\\u0436, \\\\u0431\\\\u0435\\\\u0437 \\\\u043b\\\\u0438\\\\u0444\\\\u0442\\\\u0430, \\\\u041016;\\\\r\\\\n\\\\r\\\\n\\\\u0421\\\\u043e\\\\u0441\\\\u0442\\\\u043e\\\\u0438\\\\u0442 \\\\u0438\\\\u0437 \\\\u043a\\\\u043e\\\\u0440\\\\u0438\\\\u0434\\\\u043e\\\\u0440\\\\u0430, \\\\u043a\\\\u0443\\\\u0445\\\\u043d\\\\u0438 \\\\u0441 \\\\u0433\\\\u043e\\\\u0441\\\\u0442\\\\u0438\\\\u043d\\\\u043e\\\\u0439, \\\\u0441\\\\u043f\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u0438, \\\\u0432\\\\u0430\\\\u043d\\\\u043d\\\\u043e\\\\u0439 \\\\u043a\\\\u043e\\\\u043c\\\\u043d\\\\u0430\\\\u0442\\\\u044b \\\\u0438 \\\\u0442\\\\u0435\\\\u0440\\\\u0440\\\\u0430\\\\u0441\\\\u044b.\\\\r\\\\n\\\\r\\\\n\\\\ud83d\\\\udc9b \\\\ud83d\\\\udc9b \\\\u0415\\\\u0441\\\\u0442\\\\u044c \\\\u0441\\\\u0442\\\\u0438\\\\u0440\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u0430\\\\u044f, \\\\u043f\\\\u043e\\\\u0441\\\\u0443\\\\u0434\\\\u043e\\\\u043c\\\\u043e\\\\u0435\\\\u0447\\\\u043d\\\\u0430\\\\u044f \\\\u043c\\\\u0430\\\\u0448\\\\u0438\\\\u043d\\\\u0430, \\\\u0434\\\\u0443\\\\u0445\\\\u043e\\\\u0432\\\\u043e\\\\u0439 \\\\u0448\\\\u043a\\\\u0430\\\\u0444, \\\\u0432\\\\u0430\\\\u0440\\\\u043e\\\\u0447\\\\u043d\\\\u0430\\\\u044f \\\\u043f\\\\u0430\\\\u043d\\\\u0435\\\\u043b\\\\u044c, \\\\u0447\\\\u0430\\\\u0439\\\\u043d\\\\u0438\\\\u043a, \\\\u0442\\\\u0432, \\\\u043a\\\\u043e\\\\u043d\\\\u0434\\\\u0438\\\\u0446\\\\u0438\\\\u043e\\\\u043d\\\\u0435\\\\u0440\\\\u044b.\\\\r\\\\n\\\\u2795 \\\\u0415\\\\u0441\\\\u0442\\\\u044c \\\\u043f\\\\u043e\\\\u0441\\\\u0443\\\\u0434\\\\u0430 \\\\u0438 \\\\u0431\\\\u0435\\\\u043b\\\\u044c\\\\u0435. \\\\r\\\\n\\\\r\\\\n\\\\ud83d\\\\udccc \\\\u041d\\\\u0430\\\\u0445\\\\u043e\\\\u0434\\\\u0438\\\\u0442\\\\u0441\\\\u044f \\\\u043d\\\\u0430 \\\\u0446\\\\u0435\\\\u043d\\\\u0442\\\\u0440\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u043e\\\\u0439 \\\\u043f\\\\u0435\\\\u0448\\\\u0435\\\\u0445\\\\u043e\\\\u0434\\\\u043d\\\\u043e\\\\u0439 \\\\u0443\\\\u043b\\\\u0438\\\\u0446\\\\u0435 \\\\u0411\\\\u043e\\\\u0433\\\\u043e\\\\u0440\\\\u0438\\\\u0434\\\\u0438. \\\\u0426\\\\u0435\\\\u043d\\\\u0442\\\\u0440\\\\u0430\\\\u043b\\\\u044c\\\\u043d\\\\u0435\\\\u0435 \\\\u0443\\\\u0436\\\\u0435 \\\\u043d\\\\u0435\\\\u043a\\\\u0443\\\\u0434\\\\u0430. 5 \\\\u043c\\\\u0438\\\\u043d\\\\u0443\\\\u0442 \\\\u043f\\\\u0435\\\\u0448\\\\u043a\\\\u043e\\\\u043c \\\\u0434\\\\u043e \\\\u043f\\\\u043b\\\\u044f\\\\u0436\\\\u0430. \\\\u0412\\\\u043e\\\\u043a\\\\u0440\\\\u0443\\\\u0433 \\\\u043c\\\\u0430\\\\u0433\\\\u0430\\\\u0437\\\\u0438\\\\u043d\\\\u044b \\\\u0440\\\\u0435\\\\u0441\\\\u0442\\\\u043e\\\\u0440\\\\u0430\\\\u043d\\\\u044b, \\\\u0431\\\\u0430\\\\u0440\\\\u044b, \\\\u043f\\\\u043e\\\\u0440\\\\u0442, \\\\u0434\\\\u0435\\\\u0442\\\\u0441\\\\u043a\\\\u0430\\\\u044f \\\\u043f\\\\u043b\\\\u043e\\\\u0449\\\\u0430\\\\u0434\\\\u043a\\\\u0430.\\\\r\\\\n\\\\r\\\\n\\\\u2139\\\\ufe0f \\\\u0421\\\\u0434\\\\u0430\\\\u0435\\\\u0442\\\\u0441\\\\u044f \\\\u0434\\\\u043e\\\\u043b\\\\u0433\\\\u043e\\\\u0441\\\\u0440\\\\u043e\\\\u0447\\\\u043d\\\\u043e\\\\r\\\\n\\\\u23f3 \\\\u041e\\\\u0441\\\\u0432\\\\u043e\\\\u0431\\\\u043e\\\\u0436\\\\u0434\\\\u0430\\\\u0435\\\\u0442\\\\u0441\\\\u044f 01\\\\/09\\\\r\\\\n\\\\u041e\\\\u0441\\\\u0435\\\\u043d\\\\u044c.\\\",\\\"is_featured\\\":\\\"0\\\",\\\"content\\\":\\\"<p>\\\\ud83c\\\\udfe0 1200 LEVA, Super Center, Bogoridi street,<\\\\/p><p>Two-room apartment, 120m2, 2nd middle floor, no elevator, A16;<\\\\/p><p>Consists of a corridor, kitchen with living room, bedroom, bathroom and terrace.<\\\\/p><p>\\\\ud83d\\\\udc9b \\\\ud83d\\\\udc9b There is a washing machine, dishwasher, oven, hob, kettle, TV, air conditioners.<br>\\\\u2795 There are dishes and linen.<\\\\/p><p>\\\\ud83d\\\\udccc It is located on the central pedestrian street of Bogoridi. There is nowhere more central. 5 minutes walk to the beach. Around shops, restaurants, bars, port, playground.<\\\\/p><p>\\\\u2139\\\\ufe0f Long-term rent<br>\\\\u23f3 Available 01\\\\/09<br>Autumn.<\\\\/p>\\\",\\\"images\\\":[null,\\\".\\\\/photo-2025-06-19-204222.webp\\\",\\\".\\\\/photo-2025-06-19-204226.webp\\\",\\\".\\\\/photo-2025-06-19-204225.webp\\\",\\\".\\\\/photo-2025-06-19-204224.webp\\\",\\\".\\\\/photo-2025-06-19-204227.webp\\\",\\\".\\\\/photo-2025-06-19-204223.webp\\\",\\\".\\\\/photo-2025-06-19-204221.webp\\\"],\\\"video\\\":null,\\\"country_id\\\":\\\"34\\\",\\\"state_id\\\":null,\\\"city_id\\\":\\\"8212\\\",\\\"district_id\\\":null,\\\"location\\\":\\\"Burgas, Bulgaria\\\",\\\"latitude\\\":\\\"42.50479259999999\\\",\\\"longitude\\\":\\\"27.4626361\\\",\\\"number_bedroom\\\":null,\\\"number_bathroom\\\":null,\\\"number_floor\\\":\\\"2\\\",\\\"square\\\":\\\"120\\\",\\\"price\\\":\\\"1200\\\",\\\"currency_id\\\":\\\"10\\\",\\\"commission\\\":null,\\\"deposit\\\":null,\\\"never_expired\\\":\\\"1\\\",\\\"bills_included\\\":\\\"0\\\",\\\"utilities\\\":null,\\\"furnished\\\":\\\"1\\\",\\\"pets_allowed\\\":\\\"0\\\",\\\"smoking_allowed\\\":\\\"0\\\",\\\"online_view_tour\\\":\\\"0\\\",\\\"features\\\":[\\\"3\\\",\\\"7\\\",\\\"13\\\",\\\"14\\\",\\\"16\\\"],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"apply\\\",\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"renting\\\",\\\"categories\\\":[\\\"7\\\"],\\\"unique_id\\\":null,\\\"project_id\\\":\\\"25\\\",\\\"author_id\\\":\\\"74\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "property", "updated", 1, 1, 1169, "Long-term rent of a 2-room apartment 120m² in central Burgas, Bulgaria", "primary", "2025-07-19 19:05:39", "2025-07-19 19:05:39", "{\"name\":\"Long-term rent of a 2-room apartment 120m\\u00b2 in central Burgas, Bulgaria\",\"model\":\"Xmetr\\\\RealEstate\\\\Models\\\\Property\",\"slug\":\"long-term-rent-of-a-2-room-apartment-120m2-in-central-burgas-bulgaria\",\"slug_id\":\"2882\",\"is_slug_editable\":\"1\",\"description\":null,\"original_description\":\"\\ud83c\\udfe0 1200 \\u041b\\u0415\\u0412\\u0410, \\u0421\\u0443\\u043f\\u0435\\u0440 \\u0426\\u0435\\u043d\\u0442\\u0440, \\u0443\\u043b. \\u0411\\u043e\\u0433\\u043e\\u0440\\u0438\\u0434\\u0438,\\r\\n\\r\\n\\u0414\\u0432\\u0443\\u0445\\u043a\\u043e\\u043c\\u043d\\u0430\\u0442\\u043d\\u0430\\u044f \\u043a\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430, 120\\u043c2, 2 \\u0441\\u0440\\u0435\\u0434\\u043d\\u0438\\u0439 \\u044d\\u0442\\u0430\\u0436, \\u0431\\u0435\\u0437 \\u043b\\u0438\\u0444\\u0442\\u0430, \\u041016;\\r\\n\\r\\n\\u0421\\u043e\\u0441\\u0442\\u043e\\u0438\\u0442 \\u0438\\u0437 \\u043a\\u043e\\u0440\\u0438\\u0434\\u043e\\u0440\\u0430, \\u043a\\u0443\\u0445\\u043d\\u0438 \\u0441 \\u0433\\u043e\\u0441\\u0442\\u0438\\u043d\\u043e\\u0439, \\u0441\\u043f\\u0430\\u043b\\u044c\\u043d\\u0438, \\u0432\\u0430\\u043d\\u043d\\u043e\\u0439 \\u043a\\u043e\\u043c\\u043d\\u0430\\u0442\\u044b \\u0438 \\u0442\\u0435\\u0440\\u0440\\u0430\\u0441\\u044b.\\r\\n\\r\\n\\ud83d\\udc9b \\ud83d\\udc9b \\u0415\\u0441\\u0442\\u044c \\u0441\\u0442\\u0438\\u0440\\u0430\\u043b\\u044c\\u043d\\u0430\\u044f, \\u043f\\u043e\\u0441\\u0443\\u0434\\u043e\\u043c\\u043e\\u0435\\u0447\\u043d\\u0430\\u044f \\u043c\\u0430\\u0448\\u0438\\u043d\\u0430, \\u0434\\u0443\\u0445\\u043e\\u0432\\u043e\\u0439 \\u0448\\u043a\\u0430\\u0444, \\u0432\\u0430\\u0440\\u043e\\u0447\\u043d\\u0430\\u044f \\u043f\\u0430\\u043d\\u0435\\u043b\\u044c, \\u0447\\u0430\\u0439\\u043d\\u0438\\u043a, \\u0442\\u0432, \\u043a\\u043e\\u043d\\u0434\\u0438\\u0446\\u0438\\u043e\\u043d\\u0435\\u0440\\u044b.\\r\\n\\u2795 \\u0415\\u0441\\u0442\\u044c \\u043f\\u043e\\u0441\\u0443\\u0434\\u0430 \\u0438 \\u0431\\u0435\\u043b\\u044c\\u0435. \\r\\n\\r\\n\\ud83d\\udccc \\u041d\\u0430\\u0445\\u043e\\u0434\\u0438\\u0442\\u0441\\u044f \\u043d\\u0430 \\u0446\\u0435\\u043d\\u0442\\u0440\\u0430\\u043b\\u044c\\u043d\\u043e\\u0439 \\u043f\\u0435\\u0448\\u0435\\u0445\\u043e\\u0434\\u043d\\u043e\\u0439 \\u0443\\u043b\\u0438\\u0446\\u0435 \\u0411\\u043e\\u0433\\u043e\\u0440\\u0438\\u0434\\u0438. \\u0426\\u0435\\u043d\\u0442\\u0440\\u0430\\u043b\\u044c\\u043d\\u0435\\u0435 \\u0443\\u0436\\u0435 \\u043d\\u0435\\u043a\\u0443\\u0434\\u0430. 5 \\u043c\\u0438\\u043d\\u0443\\u0442 \\u043f\\u0435\\u0448\\u043a\\u043e\\u043c \\u0434\\u043e \\u043f\\u043b\\u044f\\u0436\\u0430. \\u0412\\u043e\\u043a\\u0440\\u0443\\u0433 \\u043c\\u0430\\u0433\\u0430\\u0437\\u0438\\u043d\\u044b \\u0440\\u0435\\u0441\\u0442\\u043e\\u0440\\u0430\\u043d\\u044b, \\u0431\\u0430\\u0440\\u044b, \\u043f\\u043e\\u0440\\u0442, \\u0434\\u0435\\u0442\\u0441\\u043a\\u0430\\u044f \\u043f\\u043b\\u043e\\u0449\\u0430\\u0434\\u043a\\u0430.\\r\\n\\r\\n\\u2139\\ufe0f \\u0421\\u0434\\u0430\\u0435\\u0442\\u0441\\u044f \\u0434\\u043e\\u043b\\u0433\\u043e\\u0441\\u0440\\u043e\\u0447\\u043d\\u043e\\r\\n\\u23f3 \\u041e\\u0441\\u0432\\u043e\\u0431\\u043e\\u0436\\u0434\\u0430\\u0435\\u0442\\u0441\\u044f 01\\/09\\r\\n\\u041e\\u0441\\u0435\\u043d\\u044c.\",\"is_featured\":\"0\",\"content\":\"<p>\\ud83c\\udfe0 1200 LEVA, Super Center, Bogoridi street,<\\/p><p>Two-room apartment, 120m2, 2nd middle floor, no elevator, A16;<\\/p><p>Consists of a corridor, kitchen with living room, bedroom, bathroom and terrace.<\\/p><p>\\ud83d\\udc9b \\ud83d\\udc9b There is a washing machine, dishwasher, oven, hob, kettle, TV, air conditioners.<br>\\u2795 There are dishes and linen.<\\/p><p>\\ud83d\\udccc It is located on the central pedestrian street of Bogoridi. There is nowhere more central. 5 minutes walk to the beach. Around shops, restaurants, bars, port, playground.<\\/p><p>\\u2139\\ufe0f Long-term rent<br>\\u23f3 Available 01\\/09<br>Autumn.<\\/p>\",\"images\":[null,\".\\/photo-2025-06-19-204222.webp\",\".\\/photo-2025-06-19-204226.webp\",\".\\/photo-2025-06-19-204225.webp\",\".\\/photo-2025-06-19-204224.webp\",\".\\/photo-2025-06-19-204227.webp\",\".\\/photo-2025-06-19-204223.webp\",\".\\/photo-2025-06-19-204221.webp\"],\"video\":null,\"country_id\":\"34\",\"state_id\":null,\"city_id\":\"8212\",\"district_id\":null,\"location\":\"Burgas, Bulgaria\",\"latitude\":\"42.50479259999999\",\"longitude\":\"27.4626361\",\"number_bedroom\":null,\"number_bathroom\":null,\"number_floor\":\"2\",\"square\":\"120\",\"price\":\"1200\",\"currency_id\":\"10\",\"commission\":null,\"deposit\":null,\"never_expired\":\"1\",\"bills_included\":\"0\",\"utilities\":null,\"furnished\":\"1\",\"pets_allowed\":\"0\",\"smoking_allowed\":\"0\",\"online_view_tour\":\"0\",\"features\":[\"3\",\"7\",\"13\",\"14\",\"16\"],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"apply\",\"language\":\"en_US\",\"status\":\"renting\",\"categories\":[\"7\"],\"unique_id\":null,\"project_id\":\"25\",\"author_id\":\"74\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": **********.423717, "duration": 0.00637, "duration_str": "6.37ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 97.059, "width_percent": 2.941}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Currency": {"value": 43, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 26, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Facility": {"value": 23, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFacility.php:1", "ajax": false, "filename": "Facility.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Project": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProject.php:1", "ajax": false, "filename": "Project.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php:1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Property": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProperty.php:1", "ajax": false, "filename": "Property.php", "line": "?"}}, "Xmetr\\LanguageAdvanced\\Models\\TranslationResolver": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FModels%2FTranslationResolver.php:1", "ajax": false, "filename": "TranslationResolver.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Location\\Models\\City": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCity.php:1", "ajax": false, "filename": "City.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 114, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/properties/edit/1169", "action_name": "property.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@update", "uri": "POST admin/real-estate/properties/edit/{property}", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@update<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:120\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers", "prefix": "admin/real-estate/properties", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FPropertyController.php:120\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/PropertyController.php:120-172</a>", "middleware": "web, core, auth", "duration": "3.11s", "peak_memory": "46MB", "response": "Redirect to https://xmetr.gc/admin/real-estate/properties/edit/1169", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1023135198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1023135198\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1348004120 data-indent-pad=\"  \"><span class=sf-dump-note>array:44</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Long-term rent of a 2-room apartment 120m&#178; in central Burgas, Bulgaria</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Xmetr\\RealEstate\\Models\\Property</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"69 characters\">long-term-rent-of-a-2-room-apartment-120m2-in-central-burgas-bulgaria</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2882</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>original_description</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"530 characters\">&#127968; 1200 &#1051;&#1045;&#1042;&#1040;, &#1057;&#1091;&#1087;&#1077;&#1088; &#1062;&#1077;&#1085;&#1090;&#1088;, &#1091;&#1083;. &#1041;&#1086;&#1075;&#1086;&#1088;&#1080;&#1076;&#1080;,<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#1044;&#1074;&#1091;&#1093;&#1082;&#1086;&#1084;&#1085;&#1072;&#1090;&#1085;&#1072;&#1103; &#1082;&#1074;&#1072;&#1088;&#1090;&#1080;&#1088;&#1072;, 120&#1084;2, 2 &#1089;&#1088;&#1077;&#1076;&#1085;&#1080;&#1081; &#1101;&#1090;&#1072;&#1078;, &#1073;&#1077;&#1079; &#1083;&#1080;&#1092;&#1090;&#1072;, &#1040;16;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#1057;&#1086;&#1089;&#1090;&#1086;&#1080;&#1090; &#1080;&#1079; &#1082;&#1086;&#1088;&#1080;&#1076;&#1086;&#1088;&#1072;, &#1082;&#1091;&#1093;&#1085;&#1080; &#1089; &#1075;&#1086;&#1089;&#1090;&#1080;&#1085;&#1086;&#1081;, &#1089;&#1087;&#1072;&#1083;&#1100;&#1085;&#1080;, &#1074;&#1072;&#1085;&#1085;&#1086;&#1081; &#1082;&#1086;&#1084;&#1085;&#1072;&#1090;&#1099; &#1080; &#1090;&#1077;&#1088;&#1088;&#1072;&#1089;&#1099;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#128155; &#128155; &#1045;&#1089;&#1090;&#1100; &#1089;&#1090;&#1080;&#1088;&#1072;&#1083;&#1100;&#1085;&#1072;&#1103;, &#1087;&#1086;&#1089;&#1091;&#1076;&#1086;&#1084;&#1086;&#1077;&#1095;&#1085;&#1072;&#1103; &#1084;&#1072;&#1096;&#1080;&#1085;&#1072;, &#1076;&#1091;&#1093;&#1086;&#1074;&#1086;&#1081; &#1096;&#1082;&#1072;&#1092;, &#1074;&#1072;&#1088;&#1086;&#1095;&#1085;&#1072;&#1103; &#1087;&#1072;&#1085;&#1077;&#1083;&#1100;, &#1095;&#1072;&#1081;&#1085;&#1080;&#1082;, &#1090;&#1074;, &#1082;&#1086;&#1085;&#1076;&#1080;&#1094;&#1080;&#1086;&#1085;&#1077;&#1088;&#1099;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#10133; &#1045;&#1089;&#1090;&#1100; &#1087;&#1086;&#1089;&#1091;&#1076;&#1072; &#1080; &#1073;&#1077;&#1083;&#1100;&#1077;. <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#128204; &#1053;&#1072;&#1093;&#1086;&#1076;&#1080;&#1090;&#1089;&#1103; &#1085;&#1072; &#1094;&#1077;&#1085;&#1090;&#1088;&#1072;&#1083;&#1100;&#1085;&#1086;&#1081; &#1087;&#1077;&#1096;&#1077;&#1093;&#1086;&#1076;&#1085;&#1086;&#1081; &#1091;&#1083;&#1080;&#1094;&#1077; &#1041;&#1086;&#1075;&#1086;&#1088;&#1080;&#1076;&#1080;. &#1062;&#1077;&#1085;&#1090;&#1088;&#1072;&#1083;&#1100;&#1085;&#1077;&#1077; &#1091;&#1078;&#1077; &#1085;&#1077;&#1082;&#1091;&#1076;&#1072;. 5 &#1084;&#1080;&#1085;&#1091;&#1090; &#1087;&#1077;&#1096;&#1082;&#1086;&#1084; &#1076;&#1086; &#1087;&#1083;&#1103;&#1078;&#1072;. &#1042;&#1086;&#1082;&#1088;&#1091;&#1075; &#1084;&#1072;&#1075;&#1072;&#1079;&#1080;&#1085;&#1099; &#1088;&#1077;&#1089;&#1090;&#1086;&#1088;&#1072;&#1085;&#1099;, &#1073;&#1072;&#1088;&#1099;, &#1087;&#1086;&#1088;&#1090;, &#1076;&#1077;&#1090;&#1089;&#1082;&#1072;&#1103; &#1087;&#1083;&#1086;&#1097;&#1072;&#1076;&#1082;&#1072;.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#8505;&#65039; &#1057;&#1076;&#1072;&#1077;&#1090;&#1089;&#1103; &#1076;&#1086;&#1083;&#1075;&#1086;&#1089;&#1088;&#1086;&#1095;&#1085;&#1086;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#9203; &#1054;&#1089;&#1074;&#1086;&#1073;&#1086;&#1078;&#1076;&#1072;&#1077;&#1090;&#1089;&#1103; 01/09<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"530 characters\">&#1054;&#1089;&#1077;&#1085;&#1100;.</span>\n    \"\"\"\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"565 characters\">&lt;p&gt;&#127968; 1200 LEVA, Super Center, Bogoridi street,&lt;/p&gt;&lt;p&gt;Two-room apartment, 120m2, 2nd middle floor, no elevator, A16;&lt;/p&gt;&lt;p&gt;Consists of a corridor, kitchen with living room, bedroom, bathroom and terrace.&lt;/p&gt;&lt;p&gt;&#128155; &#128155; There is a washing machine, dishwasher, oven, hob, kettle, TV, air conditioners.&lt;br&gt;&#10133; There are dishes and linen.&lt;/p&gt;&lt;p&gt;&#128204; It is located on the central pedestrian street of Bogoridi. There is nowhere more central. 5 minutes walk to the beach. Around shops, restaurants, bars, port, playground.&lt;/p&gt;&lt;p&gt;&#8505;&#65039; Long-term rent&lt;br&gt;&#9203; Available 01/09&lt;br&gt;Autumn.&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-204222.webp</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-204226.webp</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-204225.webp</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-204224.webp</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-204227.webp</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-204223.webp</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-204221.webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>video</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n  \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8212</span>\"\n  \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Burgas, Bulgaria</span>\"\n  \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"17 characters\">42.50479259999999</span>\"\n  \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"10 characters\">27.4626361</span>\"\n  \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1200</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>commission</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>deposit</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>never_expired</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>bills_included</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>furnished</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>pets_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>smoking_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>online_view_tour</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>3</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>7</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"\n  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>author_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">74</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348004120\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1746083260 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4808</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/admin/real-estate/properties/edit/1169</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; project_wishlist=25; wishlist=657; _hjSession_6417422=eyJpZCI6Ijg3Mjk5NzNiLTU1NjUtNDEyYi1hNTZlLTJlMTk4ODQzYTk2MCIsImMiOjE3NTI5NDgxNjUzNzUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1752948165$o95$g1$t1752951607$j53$l0$h0; XSRF-TOKEN=eyJpdiI6IkljU25ucjFHUXNjN083UEE4ZG1mM3c9PSIsInZhbHVlIjoiWjJCL3ZhK3Z1OVBDM1c3NVFwMGlBTE50NTJKaEIzNms0ZzM4V0czV214ZkdSZEtkYnpjUXpTOWFQSGh4TXFMSFRFdmRnTnFnbEY3RGRaVklkYXpKYzdGMFVaR1ZBMTd2QjhQbkE4bTVoWUdGV0lkL3dPZmZnMHBXclpDajFtajIiLCJtYWMiOiI3ZDAwMDVhOGM3MDM1ZjcwMzViNzY0OTQyZDM3M2NiZWM5YTRiN2MyN2M5ZTBiMWJjNzQ1NTA5N2U1ZjU0ZTk5IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IlZ3WEVTYzczdkR1WXd2bXM0a1BEVnc9PSIsInZhbHVlIjoidVhaWFpWZnJTWHo1TVVyVDkzWnAySGpZMzlneG5pR3RxYWxsWFZadUQ3dW5HdlJpV2ZZNituZHYrd21kTEJ3bkk3UGhPZS96bnIrR2tiZVBndW1nWThhS2hIaisrNFNvdFl5aUZwUW1DQytDeHA5ZHcxWWdTcFJTVkI0b3ZKSjUiLCJtYWMiOiI4NDM5OGY4YTkwYzFiNzA2M2JmOWIyYWZjOWYzOTQ1OTBmODUxZjNhMDJmZDI3MGUxMGVhOTY5ZjQxM2UyY2NiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746083260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-852933299 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RobK1zu3t1m8xEzREPlVbKwr1yqlht8JLepj3TJp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852933299\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1840899858 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 19:05:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/admin/real-estate/properties/edit/1169</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840899858\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1670876708 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/admin/real-estate/properties/edit/1169</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>101</span> => <span class=sf-dump-num>1752951792</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>101</span> => <span class=sf-dump-num>1752951792</span>\n  </samp>]\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670876708\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/properties/edit/1169", "action_name": "property.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\PropertyController@update"}, "badge": "302 Found"}}