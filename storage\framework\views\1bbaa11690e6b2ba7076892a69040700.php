<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['box-select',  $class ?? null]); ?>">
<label for="block" class="title-select fw-5"><?php echo e(__('Blocks')); ?></label>
    <select name="block" id="block" class="select_js">
        <option value=""><?php echo e(__('All')); ?></option>
        <?php $__currentLoopData = range(1, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <option value="<?php echo e($i); ?>">
                <?php if($i < 5): ?>
                    <?php echo e($i === 1 ? __('1 Block') : __(':number Blocks', ['number' => $i])); ?>

                <?php else: ?>
                    <?php echo e(__(':number+ Blocks', ['number' => $i])); ?>

                <?php endif; ?>
            </option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </select>
</div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/filters/block.blade.php ENDPATH**/ ?>