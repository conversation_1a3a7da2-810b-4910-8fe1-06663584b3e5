<?php
    $itemsPerRow ??= 3;
?>

<?php if($projects->isNotEmpty()): ?>
    <div class="row row-cols-1 row-cols-sm-2 <?php if($itemsPerRow > 2): ?> row-cols-md-<?php echo e($itemsPerRow - 1); ?> <?php endif; ?> row-cols-xl-<?php echo e($itemsPerRow); ?>">
        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col">
                <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.projects.item-grid'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/projects/grid.blade.php ENDPATH**/ ?>