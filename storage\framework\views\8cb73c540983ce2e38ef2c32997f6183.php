<?php
    Theme::asset()->usePath()->add('leaflet', 'plugins/leaflet/leaflet.css');
    Theme::asset()->usePath()->add('leaflet-markercluster', 'plugins/leaflet/MarkerCluster.css');
    Theme::asset()->usePath()->add('leaflet-markercluster-default', 'plugins/leaflet/MarkerCluster.Default.css');
    Theme::asset()->container('footer')->usePath()->add('leaflet', 'plugins/leaflet/leaflet.js');
    Theme::asset()->container('footer')->usePath()->add('leaflet-markercluster', 'plugins/leaflet/leaflet.markercluster.js');
?>

<div
    data-bb-toggle="list-map"
    id="map"
    style="min-height: 100vh;"
    data-url="<?php echo e($mapUrl); ?>"
    data-tile-layer="<?php echo e(RealEstateHelper::getMapTileLayer()); ?>"
    data-center="<?php echo e(json_encode(RealEstateHelper::getMapCenterLatLng())); ?>"
></div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/map.blade.php ENDPATH**/ ?>