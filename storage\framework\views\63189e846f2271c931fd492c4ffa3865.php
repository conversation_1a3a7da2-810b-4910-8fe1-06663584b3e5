<?php if($property->facilities->isNotEmpty()): ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['single-property-nearby', $class ?? null]); ?>">
        <div class="h7 title fw-7"><?php echo e(__('What’s nearby?')); ?></div>
        <p class="body-2"><?php echo e(__("Explore nearby amenities to precisely locate your property and identify surrounding conveniences, providing a comprehensive overview of the living environment and the property's convenience.")); ?></p>
        <ul class="grid-3 box-nearby">
            <?php $__currentLoopData = $property->facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="item-nearby">
                    <span class="label">
                        <?php if($facility->icon): ?>
                            <?php echo BaseHelper::renderIcon($facility->icon); ?>

                        <?php endif; ?>
                        <?php echo e($facility->name); ?>:
                    </span>
                    <span class="fw-7"><?php echo e($facility->pivot->distance); ?></span>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/single-layouts/partials/facilities.blade.php ENDPATH**/ ?>