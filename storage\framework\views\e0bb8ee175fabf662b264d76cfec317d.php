<?php
    Theme::asset()->container('footer')->usePath()->add('nouislider', 'js/nouislider.min.js');
    Theme::asset()->container('footer')->usePath()->add('wnumb', 'js/wNumb.min.js');
    Theme::asset()->container('footer')->usePath()->add('nice-select', 'js/jquery.nice-select.min.js');

    $style ??= 1;
?>

<?php if($style === 'sidebar'): ?>
    <div class="flat-tab flat-tab-form widget-filter-search widget-box bg-surface">
        <div class="h7 title fw-7"><?php echo e(__('Search')); ?></div>
        <div class="form-sl">
            <div class="wd-filter-select">
                <div class="inner-group inner-filter">
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.keyword'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.location'), ['style' => 3], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.categories'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.floor'), ['class' => 'form-style'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.block'), ['class' => 'form-style'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.price'), ['class' => 'form-style'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.flat'), ['class' => 'form-style wd-price-2'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <div class="form-style">
                        <button type="submit" class="tf-btn primary"><?php echo e(__('Find Properties')); ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="flat-tab flat-tab-form">
        <div class="form-sl">
            <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.base'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <div class="wd-search-form">
                <div class="grid-2 group-box group-price">
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.price'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.flat'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="group-box">
                    <div class="group-select grid-3">
                        <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.floor'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.block'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
                <button type="submit" class="tf-btn primary search-box-offcanvas-button"><?php echo e(__('Find Projects')); ?></button>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/filters/project-search-box.blade.php ENDPATH**/ ?>