<?php
    Theme::addBodyAttributes(['class' => Theme::getBodyAttribute('class') . ' listing-no-map']);

    Theme::asset()->container('footer')->usePath()->add('nice-select', 'js/jquery.nice-select.min.js');

    $itemLayout = request()->input('layout', $itemLayout ?? 'grid');
?>

<form
    action="<?php echo e($actionUrl); ?>"
    data-url="<?php echo e($ajaxUrl); ?>"
    method="get"
    class="filter-form"
>
    <?php echo csrf_field(); ?>

    <input type="hidden" name="page" value="<?php echo e(BaseHelper::stringify(request()->integer('page'))); ?>" />
    <input type="hidden" name="layout" value="<?php echo e(BaseHelper::stringify(request()->input('layout'))); ?>" />

    <section class="flat-map">
        <div class="container">
            <div class="search-box-offcanvas container">
                <div class="search-box-offcanvas-backdrop"></div>
                <div class="search-box-offcanvas-content">
                    <div class="search-box-offcanvas-header">
                        <h3><?php echo e(__('Filter')); ?></h3>

                        <button type="button" class="btn-close" data-bb-toggle="toggle-filter-offcanvas"></button>
                    </div>

                    <div class="wrap-filter-search">
                        <?php echo $__env->make($filterViewPath, ['style' => 2], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="flat-section-v5 flat-recommended flat-recommended-v2">
        <div class="container">
            <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.listing-top'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <?php echo apply_filters('ads_render', null, 'listing_page_before'); ?>


            <div class="position-relative" data-bb-toggle="data-listing">
                <?php echo $__env->make($itemsViewPath, compact('itemLayout'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>

            <?php echo apply_filters('ads_render', null, 'listing_page_after'); ?>

        </div>
    </section>
</form>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/listing-layouts/without-map.blade.php ENDPATH**/ ?>