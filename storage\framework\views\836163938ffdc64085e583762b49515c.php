<?php
    $itemLayout ??= request()->input('layout', 'grid');
    $itemLayout = in_array($itemLayout, ['grid', 'list']) ? $itemLayout : 'grid';
    $layout ??= get_property_listing_page_layout();

    if (! isset($itemsPerRow)) {
        $itemsPerRow = $itemLayout === 'grid' ? 3 : 2;
        if (! in_array($layout, ['top-map', 'without-map'])) {
            $itemsPerRow = $itemLayout === 'grid' ? 2 : 1;
        }
    }
?>

<?php if($projects->isNotEmpty()): ?>
    <?php echo $__env->make(Theme::getThemeNamespace("views.real-estate.projects.$itemLayout"), compact('itemsPerRow'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
    <?php
        SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
    ?>
    <div class="alert alert-warning" role="alert">
        <?php echo e(__('No projects found.')); ?>

    </div>
<?php endif; ?>

<?php if($projects instanceof \Illuminate\Pagination\LengthAwarePaginator && $projects->hasPages()): ?>
    <div class="justify-content-center wd-navigation">
        <?php echo e($projects->withQueryString()->links(Theme::getThemeNamespace('partials.pagination'))); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/projects/index.blade.php ENDPATH**/ ?>