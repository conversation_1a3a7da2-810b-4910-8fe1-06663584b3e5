<?php
    $relatedProperties = app(\Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface::class)
        ->getRelatedProperties(
            $property->id,
            theme_option('number_of_related_properties', 3),
            \Xmetr\RealEstate\Facades\RealEstateHelper::getPropertyRelationsQuery()
        );
        $relatedPropertiesCount = $relatedProperties->count();
?>

<?php if($relatedProperties->isNotEmpty()): ?>
    <h3 class="title text-center pt30"><?php echo e(__('Similar listings')); ?></h3>
    <div class="grid gap-[20px] grid-cols-3 max-[1280px]:grid-cols-2 max-[768px]:grid-cols-1">
        <?php $__currentLoopData = $relatedProperties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.properties.item-grid'), ['property' => $property], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <?php if($relatedPropertiesCount > theme_option('number_of_related_properties', 3)): ?>
    <div class="w-full flex justify-center">
        <a href="<?php echo e(RealEstateHelper::getPropertiesListPageUrl()); ?>" class="px-[37px] py-[15px] bg-[#5E2DC2] rounded-[10px]">
        <p class="text-white text-[15px] font-bold"><?php echo e(__('Show more')); ?></p>
        </a>
    </div>
    <?php endif; ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/single-layouts/partials/related-properties.blade.php ENDPATH**/ ?>