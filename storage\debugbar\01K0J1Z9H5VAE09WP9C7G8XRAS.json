{"__meta": {"id": "01K0J1Z9H5VAE09WP9C7G8XRAS", "datetime": "2025-07-19 19:18:30", "utime": **********.694166, "method": "GET", "uri": "/admin/theme/options/opt-text-subsection-real-estate", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752952708.453442, "end": **********.694183, "duration": 2.240741014480591, "duration_str": "2.24s", "measures": [{"label": "Booting", "start": 1752952708.453442, "relative_start": 0, "end": **********.501475, "relative_end": **********.501475, "duration": 1.****************, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.501488, "relative_start": 1.****************, "end": **********.694186, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.518806, "relative_start": 1.***************, "end": **********.534352, "relative_end": **********.534352, "duration": 0.015546083450317383, "duration_str": "15.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.579897, "relative_start": 1.****************, "end": **********.692063, "relative_end": **********.692063, "duration": 1.***************, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: packages/theme::options", "start": **********.580887, "relative_start": 1.****************, "end": **********.580887, "relative_end": **********.580887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.title", "start": **********.586379, "relative_start": 1.***************, "end": **********.586379, "relative_end": **********.586379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.admin-list-language-chooser", "start": **********.589426, "relative_start": 1.135983943939209, "end": **********.589426, "relative_end": **********.589426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.611191, "relative_start": 1.1577489376068115, "end": **********.611191, "relative_end": **********.611191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.61923, "relative_start": 1.165787935256958, "end": **********.61923, "relative_end": **********.61923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.627216, "relative_start": 1.173774003982544, "end": **********.627216, "relative_end": **********.627216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.636186, "relative_start": 1.182743787765503, "end": **********.636186, "relative_end": **********.636186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.index", "start": **********.636883, "relative_start": 1.183440923690796, "end": **********.636883, "relative_end": **********.636883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.637675, "relative_start": 1.1842329502105713, "end": **********.637675, "relative_end": **********.637675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.actions", "start": **********.638448, "relative_start": 1.1850059032440186, "end": **********.638448, "relative_end": **********.638448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.header.index", "start": **********.638785, "relative_start": 1.185342788696289, "end": **********.638785, "relative_end": **********.638785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3f64b869f2950726c003c33ee820f09e", "start": **********.640866, "relative_start": 1.1874239444732666, "end": **********.640866, "relative_end": **********.640866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d94534278ddfbbbf22883692cb330f9b", "start": **********.664815, "relative_start": 1.2113728523254395, "end": **********.664815, "relative_end": **********.664815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7913f975bc537e937e30e1debb2fd980", "start": **********.689027, "relative_start": 1.2355849742889404, "end": **********.689027, "relative_end": **********.689027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e4da38cb82467523fefe0e7a59fc9e13", "start": **********.709, "relative_start": 1.2555580139160156, "end": **********.709, "relative_end": **********.709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::51fbd75687c3ace16f46cb2f3fa9370d", "start": **********.711769, "relative_start": 1.2583270072937012, "end": **********.711769, "relative_end": **********.711769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::945d0860bbdcbcb7a23e6c2c5f3cf8e8", "start": **********.732673, "relative_start": 1.2792308330535889, "end": **********.732673, "relative_end": **********.732673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2177fba41116eaa8d1c4ff524946351b", "start": **********.758652, "relative_start": 1.3052098751068115, "end": **********.758652, "relative_end": **********.758652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98c591260fb9f490b66346af60b2da9e", "start": **********.761681, "relative_start": 1.3082389831542969, "end": **********.761681, "relative_end": **********.761681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3059529f876a3026aab63c8ab841dd10", "start": **********.764517, "relative_start": 1.31107497215271, "end": **********.764517, "relative_end": **********.764517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e24317d2bafadd9fb8ead701247fabd", "start": **********.792985, "relative_start": 1.3395428657531738, "end": **********.792985, "relative_end": **********.792985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::600f334108391decf5adb60b56bb5ec9", "start": **********.821744, "relative_start": 1.3683018684387207, "end": **********.821744, "relative_end": **********.821744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2de5dc065a24d77973e7e4a8f1bb9ab5", "start": **********.843333, "relative_start": 1.3898909091949463, "end": **********.843333, "relative_end": **********.843333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ea6d0a9f37b9fc0945c45c53cef71274", "start": **********.864056, "relative_start": 1.410614013671875, "end": **********.864056, "relative_end": **********.864056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.887213, "relative_start": 1.4337708950042725, "end": **********.887213, "relative_end": **********.887213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.888657, "relative_start": 1.4352149963378906, "end": **********.888657, "relative_end": **********.888657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.889129, "relative_start": 1.4356868267059326, "end": **********.889129, "relative_end": **********.889129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.889542, "relative_start": 1.4361000061035156, "end": **********.889542, "relative_end": **********.889542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.890763, "relative_start": 1.4373209476470947, "end": **********.890763, "relative_end": **********.890763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.891147, "relative_start": 1.4377048015594482, "end": **********.891147, "relative_end": **********.891147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.891501, "relative_start": 1.438058853149414, "end": **********.891501, "relative_end": **********.891501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.893057, "relative_start": 1.43961501121521, "end": **********.893057, "relative_end": **********.893057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.893661, "relative_start": 1.4402189254760742, "end": **********.893661, "relative_end": **********.893661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.89537, "relative_start": 1.4419279098510742, "end": **********.89537, "relative_end": **********.89537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.895942, "relative_start": 1.4424998760223389, "end": **********.895942, "relative_end": **********.895942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.897576, "relative_start": 1.444133996963501, "end": **********.897576, "relative_end": **********.897576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.898126, "relative_start": 1.4446837902069092, "end": **********.898126, "relative_end": **********.898126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.899725, "relative_start": 1.4462828636169434, "end": **********.899725, "relative_end": **********.899725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.900267, "relative_start": 1.4468247890472412, "end": **********.900267, "relative_end": **********.900267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.901889, "relative_start": 1.4484469890594482, "end": **********.901889, "relative_end": **********.901889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.902475, "relative_start": 1.4490330219268799, "end": **********.902475, "relative_end": **********.902475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.ui-selector", "start": **********.904262, "relative_start": 1.450819969177246, "end": **********.904262, "relative_end": **********.904262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.image-check", "start": **********.086335, "relative_start": 1.6328928470611572, "end": **********.086335, "relative_end": **********.086335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.255077, "relative_start": 1.8016347885131836, "end": **********.255077, "relative_end": **********.255077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.255629, "relative_start": 1.8021869659423828, "end": **********.255629, "relative_end": **********.255629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.ui-selector", "start": **********.257238, "relative_start": 1.8037958145141602, "end": **********.257238, "relative_end": **********.257238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.image-check", "start": **********.257749, "relative_start": 1.804306983947754, "end": **********.257749, "relative_end": **********.257749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.258518, "relative_start": 1.8050758838653564, "end": **********.258518, "relative_end": **********.258518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.25891, "relative_start": 1.8054678440093994, "end": **********.25891, "relative_end": **********.25891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.ui-selector", "start": **********.260107, "relative_start": 1.8066649436950684, "end": **********.260107, "relative_end": **********.260107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.image-check", "start": **********.260683, "relative_start": 1.8072409629821777, "end": **********.260683, "relative_end": **********.260683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.261733, "relative_start": 1.808290958404541, "end": **********.261733, "relative_end": **********.261733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.262258, "relative_start": 1.8088159561157227, "end": **********.262258, "relative_end": **********.262258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.263662, "relative_start": 1.8102200031280518, "end": **********.263662, "relative_end": **********.263662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.264052, "relative_start": 1.8106098175048828, "end": **********.264052, "relative_end": **********.264052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.264411, "relative_start": 1.8109688758850098, "end": **********.264411, "relative_end": **********.264411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.2656, "relative_start": 1.8121578693389893, "end": **********.2656, "relative_end": **********.2656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.265958, "relative_start": 1.8125159740447998, "end": **********.265958, "relative_end": **********.265958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.body.index", "start": **********.266299, "relative_start": 1.812856912612915, "end": **********.266299, "relative_end": **********.266299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.admin-list-language-chooser", "start": **********.268687, "relative_start": 1.8152449131011963, "end": **********.268687, "relative_end": **********.268687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.288409, "relative_start": 1.8349668979644775, "end": **********.288409, "relative_end": **********.288409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.299326, "relative_start": 1.845883846282959, "end": **********.299326, "relative_end": **********.299326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.312524, "relative_start": 1.8590819835662842, "end": **********.312524, "relative_end": **********.312524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.325213, "relative_start": 1.8717708587646484, "end": **********.325213, "relative_end": **********.325213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.index", "start": **********.326122, "relative_start": 1.8726799488067627, "end": **********.326122, "relative_end": **********.326122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.326975, "relative_start": 1.873533010482788, "end": **********.326975, "relative_end": **********.326975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.footer.index", "start": **********.327892, "relative_start": 1.8744499683380127, "end": **********.327892, "relative_end": **********.327892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.index", "start": **********.32839, "relative_start": 1.8749477863311768, "end": **********.32839, "relative_end": **********.32839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.index", "start": **********.328924, "relative_start": 1.8754818439483643, "end": **********.328924, "relative_end": **********.328924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.329879, "relative_start": 1.8764369487762451, "end": **********.329879, "relative_end": **********.329879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.330874, "relative_start": 1.877431869506836, "end": **********.330874, "relative_end": **********.330874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.331359, "relative_start": 1.8779168128967285, "end": **********.331359, "relative_end": **********.331359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.33302, "relative_start": 1.879577875137329, "end": **********.33302, "relative_end": **********.33302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.333363, "relative_start": 1.8799209594726562, "end": **********.333363, "relative_end": **********.333363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.333912, "relative_start": 1.880469799041748, "end": **********.333912, "relative_end": **********.333912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.text-input", "start": **********.334565, "relative_start": 1.8811228275299072, "end": **********.334565, "relative_end": **********.334565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.335936, "relative_start": 1.8824939727783203, "end": **********.335936, "relative_end": **********.335936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.error", "start": **********.336532, "relative_start": 1.8830900192260742, "end": **********.336532, "relative_end": **********.336532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.337008, "relative_start": 1.883565902709961, "end": **********.337008, "relative_end": **********.337008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.338201, "relative_start": 1.8847589492797852, "end": **********.338201, "relative_end": **********.338201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc988bb05638c97d86c3bc8a9b727e31", "start": **********.340079, "relative_start": 1.8866369724273682, "end": **********.340079, "relative_end": **********.340079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.340489, "relative_start": 1.8870468139648438, "end": **********.340489, "relative_end": **********.340489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ec711b4f47c4b42ebc81a7564c1d8d33", "start": **********.342325, "relative_start": 1.8888828754425049, "end": **********.342325, "relative_end": **********.342325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.344043, "relative_start": 1.8906009197235107, "end": **********.344043, "relative_end": **********.344043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::306e03d3dc5634ee2e82192f553c6f9b", "start": **********.345621, "relative_start": 1.892179012298584, "end": **********.345621, "relative_end": **********.345621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.350693, "relative_start": 1.8972508907318115, "end": **********.350693, "relative_end": **********.350693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.35747, "relative_start": 1.9040279388427734, "end": **********.35747, "relative_end": **********.35747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.358626, "relative_start": 1.9051837921142578, "end": **********.358626, "relative_end": **********.358626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.358973, "relative_start": 1.9055309295654297, "end": **********.358973, "relative_end": **********.358973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.359972, "relative_start": 1.9065299034118652, "end": **********.359972, "relative_end": **********.359972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.index", "start": **********.360212, "relative_start": 1.9067699909210205, "end": **********.360212, "relative_end": **********.360212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.360841, "relative_start": 1.9073989391326904, "end": **********.360841, "relative_end": **********.360841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.361789, "relative_start": 1.9083468914031982, "end": **********.361789, "relative_end": **********.361789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.362039, "relative_start": 1.9085969924926758, "end": **********.362039, "relative_end": **********.362039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.363937, "relative_start": 1.9104948043823242, "end": **********.363937, "relative_end": **********.363937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.364626, "relative_start": 1.9111838340759277, "end": **********.364626, "relative_end": **********.364626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.364941, "relative_start": 1.911498785018921, "end": **********.364941, "relative_end": **********.364941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.365504, "relative_start": 1.9120619297027588, "end": **********.365504, "relative_end": **********.365504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.index", "start": **********.365732, "relative_start": 1.9122898578643799, "end": **********.365732, "relative_end": **********.365732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.366259, "relative_start": 1.9128170013427734, "end": **********.366259, "relative_end": **********.366259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.366562, "relative_start": 1.9131197929382324, "end": **********.366562, "relative_end": **********.366562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.385469, "relative_start": 1.9320268630981445, "end": **********.385469, "relative_end": **********.385469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.386955, "relative_start": 1.9335129261016846, "end": **********.386955, "relative_end": **********.386955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.3893, "relative_start": 1.9358580112457275, "end": **********.3893, "relative_end": **********.3893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.390254, "relative_start": 1.936811923980713, "end": **********.390254, "relative_end": **********.390254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.391478, "relative_start": 1.9380359649658203, "end": **********.391478, "relative_end": **********.391478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5def649a5a47936dda7e47db6ffcfa75", "start": **********.394584, "relative_start": 1.9411418437957764, "end": **********.394584, "relative_end": **********.394584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.395581, "relative_start": 1.942138910293579, "end": **********.395581, "relative_end": **********.395581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.399062, "relative_start": 1.945619821548462, "end": **********.399062, "relative_end": **********.399062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.399947, "relative_start": 1.946504831314087, "end": **********.399947, "relative_end": **********.399947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.402253, "relative_start": 1.9488108158111572, "end": **********.402253, "relative_end": **********.402253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.403195, "relative_start": 1.9497528076171875, "end": **********.403195, "relative_end": **********.403195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.405487, "relative_start": 1.95204496383667, "end": **********.405487, "relative_end": **********.405487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.406332, "relative_start": 1.9528899192810059, "end": **********.406332, "relative_end": **********.406332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.408387, "relative_start": 1.9549448490142822, "end": **********.408387, "relative_end": **********.408387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.40928, "relative_start": 1.9558379650115967, "end": **********.40928, "relative_end": **********.40928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.411347, "relative_start": 1.9579048156738281, "end": **********.411347, "relative_end": **********.411347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.412285, "relative_start": 1.9588429927825928, "end": **********.412285, "relative_end": **********.412285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.414402, "relative_start": 1.9609599113464355, "end": **********.414402, "relative_end": **********.414402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.41528, "relative_start": 1.9618380069732666, "end": **********.41528, "relative_end": **********.41528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.417144, "relative_start": 1.9637019634246826, "end": **********.417144, "relative_end": **********.417144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.417983, "relative_start": 1.964540958404541, "end": **********.417983, "relative_end": **********.417983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.419098, "relative_start": 1.96565580368042, "end": **********.419098, "relative_end": **********.419098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "start": **********.421353, "relative_start": 1.9679110050201416, "end": **********.421353, "relative_end": **********.421353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.42223, "relative_start": 1.9687879085540771, "end": **********.42223, "relative_end": **********.42223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.423404, "relative_start": 1.9699618816375732, "end": **********.423404, "relative_end": **********.423404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ca20cd1247722214b06db9aa7c493b27", "start": **********.426635, "relative_start": 1.9731929302215576, "end": **********.426635, "relative_end": **********.426635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.427608, "relative_start": 1.974165916442871, "end": **********.427608, "relative_end": **********.427608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::navbar.badge-count", "start": **********.4283, "relative_start": 1.9748578071594238, "end": **********.4283, "relative_end": **********.4283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.429043, "relative_start": 1.9756009578704834, "end": **********.429043, "relative_end": **********.429043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.430506, "relative_start": 1.9770638942718506, "end": **********.430506, "relative_end": **********.430506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.431185, "relative_start": 1.9777429103851318, "end": **********.431185, "relative_end": **********.431185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::navbar.badge-count", "start": **********.431698, "relative_start": 1.9782559871673584, "end": **********.431698, "relative_end": **********.431698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.432328, "relative_start": 1.9788858890533447, "end": **********.432328, "relative_end": **********.432328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.433625, "relative_start": 1.9801828861236572, "end": **********.433625, "relative_end": **********.433625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.434364, "relative_start": 1.980921983718872, "end": **********.434364, "relative_end": **********.434364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.435643, "relative_start": 1.9822008609771729, "end": **********.435643, "relative_end": **********.435643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.436376, "relative_start": 1.9829339981079102, "end": **********.436376, "relative_end": **********.436376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.437341, "relative_start": 1.9838988780975342, "end": **********.437341, "relative_end": **********.437341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3acd3bd206d0793e18d491720de886c", "start": **********.439947, "relative_start": 1.9865047931671143, "end": **********.439947, "relative_end": **********.439947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.440722, "relative_start": 1.9872798919677734, "end": **********.440722, "relative_end": **********.440722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.441717, "relative_start": 1.9882748126983643, "end": **********.441717, "relative_end": **********.441717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd978891e1ac33723cbffddc6658659a", "start": **********.444536, "relative_start": 1.9910938739776611, "end": **********.444536, "relative_end": **********.444536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.445407, "relative_start": 1.9919648170471191, "end": **********.445407, "relative_end": **********.445407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.446547, "relative_start": 1.9931049346923828, "end": **********.446547, "relative_end": **********.446547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.447114, "relative_start": 1.9936718940734863, "end": **********.447114, "relative_end": **********.447114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.448031, "relative_start": 1.994588851928711, "end": **********.448031, "relative_end": **********.448031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.448553, "relative_start": 1.9951109886169434, "end": **********.448553, "relative_end": **********.448553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.449199, "relative_start": 1.9957568645477295, "end": **********.449199, "relative_end": **********.449199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.450999, "relative_start": 1.9975569248199463, "end": **********.450999, "relative_end": **********.450999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.451463, "relative_start": 1.998020887374878, "end": **********.451463, "relative_end": **********.451463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.452043, "relative_start": 1.998600959777832, "end": **********.452043, "relative_end": **********.452043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.453678, "relative_start": 2.0002357959747314, "end": **********.453678, "relative_end": **********.453678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.454068, "relative_start": 2.0006258487701416, "end": **********.454068, "relative_end": **********.454068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::navbar.badge-count", "start": **********.454358, "relative_start": 2.000916004180908, "end": **********.454358, "relative_end": **********.454358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.454741, "relative_start": 2.0012989044189453, "end": **********.454741, "relative_end": **********.454741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cbce7a4c13e13a70eabb7759894a60fd", "start": **********.455804, "relative_start": 2.002362012863159, "end": **********.455804, "relative_end": **********.455804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.456219, "relative_start": 2.002776861190796, "end": **********.456219, "relative_end": **********.456219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "start": **********.457264, "relative_start": 2.003821849822998, "end": **********.457264, "relative_end": **********.457264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.457687, "relative_start": 2.004244804382324, "end": **********.457687, "relative_end": **********.457687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.45825, "relative_start": 2.004807949066162, "end": **********.45825, "relative_end": **********.45825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::351bdfbe842eff22e08f1df9d5f5beb1", "start": **********.459657, "relative_start": 2.0062148571014404, "end": **********.459657, "relative_end": **********.459657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.460084, "relative_start": 2.0066418647766113, "end": **********.460084, "relative_end": **********.460084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.460769, "relative_start": 2.00732684135437, "end": **********.460769, "relative_end": **********.460769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.461174, "relative_start": 2.0077319145202637, "end": **********.461174, "relative_end": **********.461174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.462274, "relative_start": 2.0088319778442383, "end": **********.462274, "relative_end": **********.462274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.46311, "relative_start": 2.0096678733825684, "end": **********.46311, "relative_end": **********.46311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.464044, "relative_start": 2.0106019973754883, "end": **********.464044, "relative_end": **********.464044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.46597, "relative_start": 2.0125279426574707, "end": **********.46597, "relative_end": **********.46597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.46674, "relative_start": 2.0132977962493896, "end": **********.46674, "relative_end": **********.46674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.467734, "relative_start": 2.014292001724243, "end": **********.467734, "relative_end": **********.467734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.470046, "relative_start": 2.016603946685791, "end": **********.470046, "relative_end": **********.470046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.470876, "relative_start": 2.0174338817596436, "end": **********.470876, "relative_end": **********.470876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::15422be7d0f2aaf8c8244c1e9db20ad9", "start": **********.473411, "relative_start": 2.0199689865112305, "end": **********.473411, "relative_end": **********.473411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.474225, "relative_start": 2.020782947540283, "end": **********.474225, "relative_end": **********.474225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2e5e965add6d0ee3aadb02ca38e70825", "start": **********.476676, "relative_start": 2.0232338905334473, "end": **********.476676, "relative_end": **********.476676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.477403, "relative_start": 2.023960828781128, "end": **********.477403, "relative_end": **********.477403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5b8d99843e0f8eff6046d7236026b187", "start": **********.479805, "relative_start": 2.026362895965576, "end": **********.479805, "relative_end": **********.479805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.480318, "relative_start": 2.0268759727478027, "end": **********.480318, "relative_end": **********.480318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd248739db8ba923ebfe1f426bb71a38", "start": **********.482085, "relative_start": 2.0286428928375244, "end": **********.482085, "relative_end": **********.482085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.482572, "relative_start": 2.029129981994629, "end": **********.482572, "relative_end": **********.482572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.484081, "relative_start": 2.0306389331817627, "end": **********.484081, "relative_end": **********.484081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.484508, "relative_start": 2.0310659408569336, "end": **********.484508, "relative_end": **********.484508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "start": **********.485835, "relative_start": 2.032392978668213, "end": **********.485835, "relative_end": **********.485835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.486261, "relative_start": 2.0328187942504883, "end": **********.486261, "relative_end": **********.486261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.486831, "relative_start": 2.03338885307312, "end": **********.486831, "relative_end": **********.486831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.488523, "relative_start": 2.035080909729004, "end": **********.488523, "relative_end": **********.488523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.48894, "relative_start": 2.0354979038238525, "end": **********.48894, "relative_end": **********.48894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.489495, "relative_start": 2.036052942276001, "end": **********.489495, "relative_end": **********.489495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dac323985d9d2618ad252313442aaf03", "start": **********.490516, "relative_start": 2.037073850631714, "end": **********.490516, "relative_end": **********.490516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.490938, "relative_start": 2.0374958515167236, "end": **********.490938, "relative_end": **********.490938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::298cd8a12b86a6f371ff06491a0822fa", "start": **********.491844, "relative_start": 2.0384018421173096, "end": **********.491844, "relative_end": **********.491844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.492257, "relative_start": 2.0388150215148926, "end": **********.492257, "relative_end": **********.492257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c59870a61b233f0766e3260625bdb025", "start": **********.493272, "relative_start": 2.039829969406128, "end": **********.493272, "relative_end": **********.493272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.493683, "relative_start": 2.040241003036499, "end": **********.493683, "relative_end": **********.493683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3890eca5d46a147ef99ddf994453d2ec", "start": **********.495205, "relative_start": 2.0417628288269043, "end": **********.495205, "relative_end": **********.495205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.496168, "relative_start": 2.0427258014678955, "end": **********.496168, "relative_end": **********.496168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff1fde71531b073b2c658173537aaea5", "start": **********.498389, "relative_start": 2.0449469089508057, "end": **********.498389, "relative_end": **********.498389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.498989, "relative_start": 2.0455470085144043, "end": **********.498989, "relative_end": **********.498989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "start": **********.500715, "relative_start": 2.0472729206085205, "end": **********.500715, "relative_end": **********.500715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.501301, "relative_start": 2.047858953475952, "end": **********.501301, "relative_end": **********.501301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::89cb89d3fdb0a0a12f8aa61073c231e6", "start": **********.502916, "relative_start": 2.049474000930786, "end": **********.502916, "relative_end": **********.502916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.503508, "relative_start": 2.0500659942626953, "end": **********.503508, "relative_end": **********.503508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5a5b09d3f2ee0ddb2b536feb532d230a", "start": **********.505146, "relative_start": 2.051703929901123, "end": **********.505146, "relative_end": **********.505146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.505711, "relative_start": 2.0522689819335938, "end": **********.505711, "relative_end": **********.505711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fedc652debeb23dcbb31a98830baa397", "start": **********.507156, "relative_start": 2.053713798522949, "end": **********.507156, "relative_end": **********.507156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.507685, "relative_start": 2.0542428493499756, "end": **********.507685, "relative_end": **********.507685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.508358, "relative_start": 2.0549159049987793, "end": **********.508358, "relative_end": **********.508358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff3b2cf4e42e74e63db76ff05c5f2374", "start": **********.510154, "relative_start": 2.0567119121551514, "end": **********.510154, "relative_end": **********.510154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.510896, "relative_start": 2.0574538707733154, "end": **********.510896, "relative_end": **********.510896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.512003, "relative_start": 2.058560848236084, "end": **********.512003, "relative_end": **********.512003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::745871da7c635a3f461dfaeeef54a48e", "start": **********.51347, "relative_start": 2.060027837753296, "end": **********.51347, "relative_end": **********.51347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.513935, "relative_start": 2.060492992401123, "end": **********.513935, "relative_end": **********.513935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.51508, "relative_start": 2.0616378784179688, "end": **********.51508, "relative_end": **********.51508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.515502, "relative_start": 2.0620598793029785, "end": **********.515502, "relative_end": **********.515502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.516068, "relative_start": 2.0626258850097656, "end": **********.516068, "relative_end": **********.516068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.517171, "relative_start": 2.0637288093566895, "end": **********.517171, "relative_end": **********.517171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.517588, "relative_start": 2.064145803451538, "end": **********.517588, "relative_end": **********.517588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.518139, "relative_start": 2.064696788787842, "end": **********.518139, "relative_end": **********.518139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13663c834a4ae876ef8f72aa0610e8c", "start": **********.51917, "relative_start": 2.065727949142456, "end": **********.51917, "relative_end": **********.51917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.519697, "relative_start": 2.0662548542022705, "end": **********.519697, "relative_end": **********.519697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.520047, "relative_start": 2.0666048526763916, "end": **********.520047, "relative_end": **********.520047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.520858, "relative_start": 2.067415952682495, "end": **********.520858, "relative_end": **********.520858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.521196, "relative_start": 2.067753791809082, "end": **********.521196, "relative_end": **********.521196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.522162, "relative_start": 2.0687198638916016, "end": **********.522162, "relative_end": **********.522162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.522682, "relative_start": 2.069239854812622, "end": **********.522682, "relative_end": **********.522682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3cec1c87224222bda738c53f782c5bc1", "start": **********.524079, "relative_start": 2.0706369876861572, "end": **********.524079, "relative_end": **********.524079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.text-input", "start": **********.524293, "relative_start": 2.0708508491516113, "end": **********.524293, "relative_end": **********.524293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.525181, "relative_start": 2.0717389583587646, "end": **********.525181, "relative_end": **********.525181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.error", "start": **********.525558, "relative_start": 2.072115898132324, "end": **********.525558, "relative_end": **********.525558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.525911, "relative_start": 2.0724689960479736, "end": **********.525911, "relative_end": **********.525911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.index", "start": **********.526369, "relative_start": 2.0729269981384277, "end": **********.526369, "relative_end": **********.526369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::414e4e803eeec6389552bb46515583c5", "start": **********.527429, "relative_start": 2.0739870071411133, "end": **********.527429, "relative_end": **********.527429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c47a448d99d5719cb034f7947c739ff8", "start": **********.528392, "relative_start": 2.0749499797821045, "end": **********.528392, "relative_end": **********.528392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e226165e1fca7eccb10f6857d7cd235a", "start": **********.529401, "relative_start": 2.0759589672088623, "end": **********.529401, "relative_end": **********.529401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal", "start": **********.529836, "relative_start": 2.0763938426971436, "end": **********.529836, "relative_end": **********.529836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::custom-template", "start": **********.531362, "relative_start": 2.0779199600219727, "end": **********.531362, "relative_end": **********.531362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.531922, "relative_start": 2.0784800052642822, "end": **********.531922, "relative_end": **********.531922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.close-button", "start": **********.533092, "relative_start": 2.0796499252319336, "end": **********.533092, "relative_end": **********.533092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::loading", "start": **********.533544, "relative_start": 2.08010196685791, "end": **********.533544, "relative_end": **********.533544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.text-input", "start": **********.534159, "relative_start": 2.080716848373413, "end": **********.534159, "relative_end": **********.534159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.535187, "relative_start": 2.081744909286499, "end": **********.535187, "relative_end": **********.535187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.error", "start": **********.53564, "relative_start": 2.082197904586792, "end": **********.53564, "relative_end": **********.53564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form-group", "start": **********.535963, "relative_start": 2.0825209617614746, "end": **********.535963, "relative_end": **********.535963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.checkbox", "start": **********.536377, "relative_start": 2.082934856414795, "end": **********.536377, "relative_end": **********.536377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.537595, "relative_start": 2.084152936935425, "end": **********.537595, "relative_end": **********.537595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.538473, "relative_start": 2.0850307941436768, "end": **********.538473, "relative_end": **********.538473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal", "start": **********.539129, "relative_start": 2.0856869220733643, "end": **********.539129, "relative_end": **********.539129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.close-button", "start": **********.53999, "relative_start": 2.0865478515625, "end": **********.53999, "relative_end": **********.53999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.540426, "relative_start": 2.0869839191436768, "end": **********.540426, "relative_end": **********.540426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::debug-badge", "start": **********.666519, "relative_start": 2.2130768299102783, "end": **********.666519, "relative_end": **********.666519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.action", "start": **********.667176, "relative_start": 2.2137339115142822, "end": **********.667176, "relative_end": **********.667176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.alert", "start": **********.667945, "relative_start": 2.2145028114318848, "end": **********.667945, "relative_end": **********.667945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.close-button", "start": **********.668789, "relative_start": 2.2153468132019043, "end": **********.668789, "relative_end": **********.668789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dcf17957c4aa053a618fd9c312cc29fc", "start": **********.669721, "relative_start": 2.2162787914276123, "end": **********.669721, "relative_end": **********.669721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal", "start": **********.669953, "relative_start": 2.2165110111236572, "end": **********.669953, "relative_end": **********.669953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.action", "start": **********.670881, "relative_start": 2.2174389362335205, "end": **********.670881, "relative_end": **********.670881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.alert", "start": **********.671531, "relative_start": 2.2180888652801514, "end": **********.671531, "relative_end": **********.671531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal.close-button", "start": **********.672192, "relative_start": 2.21875, "end": **********.672192, "relative_end": **********.672192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fcfb9f9ba5fb460899f38b71f491e1fe", "start": **********.672959, "relative_start": 2.2195169925689697, "end": **********.672959, "relative_end": **********.672959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::modal", "start": **********.6732, "relative_start": 2.2197577953338623, "end": **********.6732, "relative_end": **********.6732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::layouts.base", "start": **********.67405, "relative_start": 2.2206079959869385, "end": **********.67405, "relative_end": **********.67405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.674888, "relative_start": 2.2214457988739014, "end": **********.674888, "relative_end": **********.674888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.686502, "relative_start": 2.233059883117676, "end": **********.686502, "relative_end": **********.686502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.688433, "relative_start": 2.2349908351898193, "end": **********.688433, "relative_end": **********.688433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.690587, "relative_start": 2.237144947052002, "end": **********.690587, "relative_end": **********.690587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.691442, "relative_start": 2.23799991607666, "end": **********.691442, "relative_end": **********.691442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 55688256, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 267, "nb_templates": 267, "templates": [{"name": "1x packages/theme::options", "param_count": null, "params": [], "start": **********.580845, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/theme/resources/views/options.blade.phppackages/theme::options", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Foptions.blade.php:1", "ajax": false, "filename": "options.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::options"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.title", "param_count": null, "params": [], "start": **********.586347, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/title.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.title", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php:1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.title"}, {"name": "2x plugins/language::partials.admin-list-language-chooser", "param_count": null, "params": [], "start": **********.589405, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/admin-list-language-chooser.blade.phpplugins/language::partials.admin-list-language-chooser", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fadmin-list-language-chooser.blade.php:1", "ajax": false, "filename": "admin-list-language-chooser.blade.php", "line": "?"}, "render_count": 2, "name_original": "plugins/language::partials.admin-list-language-chooser"}, {"name": "12x a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.611167, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php:1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 12, "name_original": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item"}, {"name": "4x a74ad8dfacd4f985eb3977517615ce25::dropdown.index", "param_count": null, "params": [], "start": **********.636861, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/dropdown/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "a74ad8dfacd4f985eb3977517615ce25::dropdown.index"}, {"name": "5x a74ad8dfacd4f985eb3977517615ce25::button", "param_count": null, "params": [], "start": **********.637643, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/button.blade.phpa74ad8dfacd4f985eb3977517615ce25::button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php:1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 5, "name_original": "a74ad8dfacd4f985eb3977517615ce25::button"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.actions", "param_count": null, "params": [], "start": **********.638426, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/actions.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.actions", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php:1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.actions"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.header.index", "param_count": null, "params": [], "start": **********.638763, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/header/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.header.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.header.index"}, {"name": "1x __components::3f64b869f2950726c003c33ee820f09e", "param_count": null, "params": [], "start": **********.640846, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3f64b869f2950726c003c33ee820f09e.blade.php__components::3f64b869f2950726c003c33ee820f09e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3f64b869f2950726c003c33ee820f09e.blade.php:1", "ajax": false, "filename": "3f64b869f2950726c003c33ee820f09e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3f64b869f2950726c003c33ee820f09e"}, {"name": "1x __components::d94534278ddfbbbf22883692cb330f9b", "param_count": null, "params": [], "start": **********.664794, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/d94534278ddfbbbf22883692cb330f9b.blade.php__components::d94534278ddfbbbf22883692cb330f9b", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fd94534278ddfbbbf22883692cb330f9b.blade.php:1", "ajax": false, "filename": "d94534278ddfbbbf22883692cb330f9b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d94534278ddfbbbf22883692cb330f9b"}, {"name": "1x __components::7913f975bc537e937e30e1debb2fd980", "param_count": null, "params": [], "start": **********.689005, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/7913f975bc537e937e30e1debb2fd980.blade.php__components::7913f975bc537e937e30e1debb2fd980", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F7913f975bc537e937e30e1debb2fd980.blade.php:1", "ajax": false, "filename": "7913f975bc537e937e30e1debb2fd980.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7913f975bc537e937e30e1debb2fd980"}, {"name": "1x __components::e4da38cb82467523fefe0e7a59fc9e13", "param_count": null, "params": [], "start": **********.708977, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/e4da38cb82467523fefe0e7a59fc9e13.blade.php__components::e4da38cb82467523fefe0e7a59fc9e13", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fe4da38cb82467523fefe0e7a59fc9e13.blade.php:1", "ajax": false, "filename": "e4da38cb82467523fefe0e7a59fc9e13.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e4da38cb82467523fefe0e7a59fc9e13"}, {"name": "1x __components::51fbd75687c3ace16f46cb2f3fa9370d", "param_count": null, "params": [], "start": **********.711747, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/51fbd75687c3ace16f46cb2f3fa9370d.blade.php__components::51fbd75687c3ace16f46cb2f3fa9370d", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F51fbd75687c3ace16f46cb2f3fa9370d.blade.php:1", "ajax": false, "filename": "51fbd75687c3ace16f46cb2f3fa9370d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::51fbd75687c3ace16f46cb2f3fa9370d"}, {"name": "1x __components::945d0860bbdcbcb7a23e6c2c5f3cf8e8", "param_count": null, "params": [], "start": **********.732649, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/945d0860bbdcbcb7a23e6c2c5f3cf8e8.blade.php__components::945d0860bbdcbcb7a23e6c2c5f3cf8e8", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F945d0860bbdcbcb7a23e6c2c5f3cf8e8.blade.php:1", "ajax": false, "filename": "945d0860bbdcbcb7a23e6c2c5f3cf8e8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::945d0860bbdcbcb7a23e6c2c5f3cf8e8"}, {"name": "1x __components::2177fba41116eaa8d1c4ff524946351b", "param_count": null, "params": [], "start": **********.75862, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2177fba41116eaa8d1c4ff524946351b.blade.php__components::2177fba41116eaa8d1c4ff524946351b", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2177fba41116eaa8d1c4ff524946351b.blade.php:1", "ajax": false, "filename": "2177fba41116eaa8d1c4ff524946351b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2177fba41116eaa8d1c4ff524946351b"}, {"name": "1x __components::98c591260fb9f490b66346af60b2da9e", "param_count": null, "params": [], "start": **********.761645, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98c591260fb9f490b66346af60b2da9e.blade.php__components::98c591260fb9f490b66346af60b2da9e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98c591260fb9f490b66346af60b2da9e.blade.php:1", "ajax": false, "filename": "98c591260fb9f490b66346af60b2da9e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::98c591260fb9f490b66346af60b2da9e"}, {"name": "1x __components::3059529f876a3026aab63c8ab841dd10", "param_count": null, "params": [], "start": **********.764495, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3059529f876a3026aab63c8ab841dd10.blade.php__components::3059529f876a3026aab63c8ab841dd10", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3059529f876a3026aab63c8ab841dd10.blade.php:1", "ajax": false, "filename": "3059529f876a3026aab63c8ab841dd10.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3059529f876a3026aab63c8ab841dd10"}, {"name": "1x __components::7e24317d2bafadd9fb8ead701247fabd", "param_count": null, "params": [], "start": **********.792951, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/7e24317d2bafadd9fb8ead701247fabd.blade.php__components::7e24317d2bafadd9fb8ead701247fabd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F7e24317d2bafadd9fb8ead701247fabd.blade.php:1", "ajax": false, "filename": "7e24317d2bafadd9fb8ead701247fabd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e24317d2bafadd9fb8ead701247fabd"}, {"name": "1x __components::600f334108391decf5adb60b56bb5ec9", "param_count": null, "params": [], "start": **********.82171, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/600f334108391decf5adb60b56bb5ec9.blade.php__components::600f334108391decf5adb60b56bb5ec9", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F600f334108391decf5adb60b56bb5ec9.blade.php:1", "ajax": false, "filename": "600f334108391decf5adb60b56bb5ec9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::600f334108391decf5adb60b56bb5ec9"}, {"name": "1x __components::2de5dc065a24d77973e7e4a8f1bb9ab5", "param_count": null, "params": [], "start": **********.84331, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2de5dc065a24d77973e7e4a8f1bb9ab5.blade.php__components::2de5dc065a24d77973e7e4a8f1bb9ab5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2de5dc065a24d77973e7e4a8f1bb9ab5.blade.php:1", "ajax": false, "filename": "2de5dc065a24d77973e7e4a8f1bb9ab5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2de5dc065a24d77973e7e4a8f1bb9ab5"}, {"name": "1x __components::ea6d0a9f37b9fc0945c45c53cef71274", "param_count": null, "params": [], "start": **********.864034, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ea6d0a9f37b9fc0945c45c53cef71274.blade.php__components::ea6d0a9f37b9fc0945c45c53cef71274", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fea6d0a9f37b9fc0945c45c53cef71274.blade.php:1", "ajax": false, "filename": "ea6d0a9f37b9fc0945c45c53cef71274.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ea6d0a9f37b9fc0945c45c53cef71274"}, {"name": "15x a74ad8dfacd4f985eb3977517615ce25::form.label", "param_count": null, "params": [], "start": **********.887191, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/label.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.label", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php:1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 15, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.label"}, {"name": "4x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": **********.888635, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php:1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.partials.custom-select"}, {"name": "15x a74ad8dfacd4f985eb3977517615ce25::form-group", "param_count": null, "params": [], "start": **********.88911, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form-group.blade.phpa74ad8dfacd4f985eb3977517615ce25::form-group", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php:1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 15, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form-group"}, {"name": "3x core/base::forms.partials.ui-selector", "param_count": null, "params": [], "start": **********.904218, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/ui-selector.blade.phpcore/base::forms.partials.ui-selector", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fui-selector.blade.php:1", "ajax": false, "filename": "ui-selector.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.partials.ui-selector"}, {"name": "3x a74ad8dfacd4f985eb3977517615ce25::form.image-check", "param_count": null, "params": [], "start": **********.086309, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/image-check.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.image-check", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimage-check.blade.php:1", "ajax": false, "filename": "image-check.blade.php", "line": "?"}, "render_count": 3, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.image-check"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.body.index", "param_count": null, "params": [], "start": **********.266279, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/body/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.body.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.body.index"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.footer.index", "param_count": null, "params": [], "start": **********.327861, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/footer/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.footer.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ffooter%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.footer.index"}, {"name": "2x a74ad8dfacd4f985eb3977517615ce25::form.index", "param_count": null, "params": [], "start": **********.32836, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.index"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.index", "param_count": null, "params": [], "start": **********.328896, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.index"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.329851, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php:1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.330845, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php:1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.33133, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.33299, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php:1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.333335, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php:1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.333882, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php:1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "3x a74ad8dfacd4f985eb3977517615ce25::form.text-input", "param_count": null, "params": [], "start": **********.334536, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/text-input.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.text-input", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php:1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 3, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.text-input"}, {"name": "3x a74ad8dfacd4f985eb3977517615ce25::form.error", "param_count": null, "params": [], "start": **********.336503, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/error.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.error", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php:1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 3, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.error"}, {"name": "1x __components::dc988bb05638c97d86c3bc8a9b727e31", "param_count": null, "params": [], "start": **********.340048, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dc988bb05638c97d86c3bc8a9b727e31.blade.php__components::dc988bb05638c97d86c3bc8a9b727e31", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdc988bb05638c97d86c3bc8a9b727e31.blade.php:1", "ajax": false, "filename": "dc988bb05638c97d86c3bc8a9b727e31.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc988bb05638c97d86c3bc8a9b727e31"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.340459, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php:1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::ec711b4f47c4b42ebc81a7564c1d8d33", "param_count": null, "params": [], "start": **********.34229, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ec711b4f47c4b42ebc81a7564c1d8d33.blade.php__components::ec711b4f47c4b42ebc81a7564c1d8d33", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fec711b4f47c4b42ebc81a7564c1d8d33.blade.php:1", "ajax": false, "filename": "ec711b4f47c4b42ebc81a7564c1d8d33.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ec711b4f47c4b42ebc81a7564c1d8d33"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.344008, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php:1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::306e03d3dc5634ee2e82192f553c6f9b", "param_count": null, "params": [], "start": **********.345581, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/306e03d3dc5634ee2e82192f553c6f9b.blade.php__components::306e03d3dc5634ee2e82192f553c6f9b", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F306e03d3dc5634ee2e82192f553c6f9b.blade.php:1", "ajax": false, "filename": "306e03d3dc5634ee2e82192f553c6f9b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::306e03d3dc5634ee2e82192f553c6f9b"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.350672, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php:1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "2x __components::2907df26a6102c24ab0c37391217b338", "param_count": null, "params": [], "start": **********.358606, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2907df26a6102c24ab0c37391217b338.blade.php__components::2907df26a6102c24ab0c37391217b338", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2907df26a6102c24ab0c37391217b338.blade.php:1", "ajax": false, "filename": "2907df26a6102c24ab0c37391217b338.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::2907df26a6102c24ab0c37391217b338"}, {"name": "2x __components::f38bffca7b8a1a50e97a6950ffd66c5c", "param_count": null, "params": [], "start": **********.359952, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f38bffca7b8a1a50e97a6950ffd66c5c.blade.php__components::f38bffca7b8a1a50e97a6950ffd66c5c", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff38bffca7b8a1a50e97a6950ffd66c5c.blade.php:1", "ajax": false, "filename": "f38bffca7b8a1a50e97a6950ffd66c5c.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f38bffca7b8a1a50e97a6950ffd66c5c"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.360822, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php:1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.36624, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php:1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.366544, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php:1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "17x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.385422, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php:1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 17, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "48x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.386913, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php:1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 48, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.389259, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php:1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::af68cda57c5ca67f3b8a7729953880bc"}, {"name": "1x __components::5def649a5a47936dda7e47db6ffcfa75", "param_count": null, "params": [], "start": **********.394544, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5def649a5a47936dda7e47db6ffcfa75.blade.php__components::5def649a5a47936dda7e47db6ffcfa75", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5def649a5a47936dda7e47db6ffcfa75.blade.php:1", "ajax": false, "filename": "5def649a5a47936dda7e47db6ffcfa75.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5def649a5a47936dda7e47db6ffcfa75"}, {"name": "14x __components::d890ecc3acbc4ef41a8ece9e81698457", "param_count": null, "params": [], "start": **********.399025, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/d890ecc3acbc4ef41a8ece9e81698457.blade.php__components::d890ecc3acbc4ef41a8ece9e81698457", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fd890ecc3acbc4ef41a8ece9e81698457.blade.php:1", "ajax": false, "filename": "d890ecc3acbc4ef41a8ece9e81698457.blade.php", "line": "?"}, "render_count": 14, "name_original": "__components::d890ecc3acbc4ef41a8ece9e81698457"}, {"name": "1x __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "param_count": null, "params": [], "start": **********.421318, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php__components::dd4c2087b0a47210b5b4e3ee87ef3eca", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd4c2087b0a47210b5b4e3ee87ef3eca.blade.php:1", "ajax": false, "filename": "dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd4c2087b0a47210b5b4e3ee87ef3eca"}, {"name": "1x __components::ca20cd1247722214b06db9aa7c493b27", "param_count": null, "params": [], "start": **********.426598, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ca20cd1247722214b06db9aa7c493b27.blade.php__components::ca20cd1247722214b06db9aa7c493b27", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fca20cd1247722214b06db9aa7c493b27.blade.php:1", "ajax": false, "filename": "ca20cd1247722214b06db9aa7c493b27.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ca20cd1247722214b06db9aa7c493b27"}, {"name": "3x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.427573, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php:1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "3x a74ad8dfacd4f985eb3977517615ce25::navbar.badge-count", "param_count": null, "params": [], "start": **********.428265, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/navbar/badge-count.blade.phpa74ad8dfacd4f985eb3977517615ce25::navbar.badge-count", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php:1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 3, "name_original": "a74ad8dfacd4f985eb3977517615ce25::navbar.badge-count"}, {"name": "1x __components::a3acd3bd206d0793e18d491720de886c", "param_count": null, "params": [], "start": **********.439911, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a3acd3bd206d0793e18d491720de886c.blade.php__components::a3acd3bd206d0793e18d491720de886c", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa3acd3bd206d0793e18d491720de886c.blade.php:1", "ajax": false, "filename": "a3acd3bd206d0793e18d491720de886c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a3acd3bd206d0793e18d491720de886c"}, {"name": "1x __components::fd978891e1ac33723cbffddc6658659a", "param_count": null, "params": [], "start": **********.444501, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fd978891e1ac33723cbffddc6658659a.blade.php__components::fd978891e1ac33723cbffddc6658659a", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffd978891e1ac33723cbffddc6658659a.blade.php:1", "ajax": false, "filename": "fd978891e1ac33723cbffddc6658659a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fd978891e1ac33723cbffddc6658659a"}, {"name": "1x __components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.450979, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php:1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::13365b7e5a448d13150fdb4b3884b510"}, {"name": "2x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.453659, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php:1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "1x __components::cbce7a4c13e13a70eabb7759894a60fd", "param_count": null, "params": [], "start": **********.455785, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/cbce7a4c13e13a70eabb7759894a60fd.blade.php__components::cbce7a4c13e13a70eabb7759894a60fd", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fcbce7a4c13e13a70eabb7759894a60fd.blade.php:1", "ajax": false, "filename": "cbce7a4c13e13a70eabb7759894a60fd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cbce7a4c13e13a70eabb7759894a60fd"}, {"name": "1x __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "param_count": null, "params": [], "start": **********.457246, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php__components::fe6fcb7551f99a7d9d1c5a4c0011f471", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffe6fcb7551f99a7d9d1c5a4c0011f471.blade.php:1", "ajax": false, "filename": "fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fe6fcb7551f99a7d9d1c5a4c0011f471"}, {"name": "1x __components::351bdfbe842eff22e08f1df9d5f5beb1", "param_count": null, "params": [], "start": **********.459638, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/351bdfbe842eff22e08f1df9d5f5beb1.blade.php__components::351bdfbe842eff22e08f1df9d5f5beb1", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F351bdfbe842eff22e08f1df9d5f5beb1.blade.php:1", "ajax": false, "filename": "351bdfbe842eff22e08f1df9d5f5beb1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::351bdfbe842eff22e08f1df9d5f5beb1"}, {"name": "1x __components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.470011, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php:1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0343a1b0800146d7d9cf6a9514ec7bf4"}, {"name": "1x __components::15422be7d0f2aaf8c8244c1e9db20ad9", "param_count": null, "params": [], "start": **********.473377, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/15422be7d0f2aaf8c8244c1e9db20ad9.blade.php__components::15422be7d0f2aaf8c8244c1e9db20ad9", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F15422be7d0f2aaf8c8244c1e9db20ad9.blade.php:1", "ajax": false, "filename": "15422be7d0f2aaf8c8244c1e9db20ad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::15422be7d0f2aaf8c8244c1e9db20ad9"}, {"name": "1x __components::2e5e965add6d0ee3aadb02ca38e70825", "param_count": null, "params": [], "start": **********.476642, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2e5e965add6d0ee3aadb02ca38e70825.blade.php__components::2e5e965add6d0ee3aadb02ca38e70825", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2e5e965add6d0ee3aadb02ca38e70825.blade.php:1", "ajax": false, "filename": "2e5e965add6d0ee3aadb02ca38e70825.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2e5e965add6d0ee3aadb02ca38e70825"}, {"name": "1x __components::5b8d99843e0f8eff6046d7236026b187", "param_count": null, "params": [], "start": **********.479781, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5b8d99843e0f8eff6046d7236026b187.blade.php__components::5b8d99843e0f8eff6046d7236026b187", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5b8d99843e0f8eff6046d7236026b187.blade.php:1", "ajax": false, "filename": "5b8d99843e0f8eff6046d7236026b187.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5b8d99843e0f8eff6046d7236026b187"}, {"name": "1x __components::dd248739db8ba923ebfe1f426bb71a38", "param_count": null, "params": [], "start": **********.482052, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd248739db8ba923ebfe1f426bb71a38.blade.php__components::dd248739db8ba923ebfe1f426bb71a38", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd248739db8ba923ebfe1f426bb71a38.blade.php:1", "ajax": false, "filename": "dd248739db8ba923ebfe1f426bb71a38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd248739db8ba923ebfe1f426bb71a38"}, {"name": "2x __components::1d6f928aaf1e585d3246cb3bee8fdd45", "param_count": null, "params": [], "start": **********.484062, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/1d6f928aaf1e585d3246cb3bee8fdd45.blade.php__components::1d6f928aaf1e585d3246cb3bee8fdd45", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F1d6f928aaf1e585d3246cb3bee8fdd45.blade.php:1", "ajax": false, "filename": "1d6f928aaf1e585d3246cb3bee8fdd45.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d6f928aaf1e585d3246cb3bee8fdd45"}, {"name": "1x __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "param_count": null, "params": [], "start": **********.485816, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Facbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php:1", "ajax": false, "filename": "acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe"}, {"name": "1x __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "param_count": null, "params": [], "start": **********.488505, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php__components::6e0b6ed9bf49c6ad9d02af2c7f911103", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php:1", "ajax": false, "filename": "6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6e0b6ed9bf49c6ad9d02af2c7f911103"}, {"name": "1x __components::dac323985d9d2618ad252313442aaf03", "param_count": null, "params": [], "start": **********.490498, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dac323985d9d2618ad252313442aaf03.blade.php__components::dac323985d9d2618ad252313442aaf03", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdac323985d9d2618ad252313442aaf03.blade.php:1", "ajax": false, "filename": "dac323985d9d2618ad252313442aaf03.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dac323985d9d2618ad252313442aaf03"}, {"name": "1x __components::298cd8a12b86a6f371ff06491a0822fa", "param_count": null, "params": [], "start": **********.491825, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/298cd8a12b86a6f371ff06491a0822fa.blade.php__components::298cd8a12b86a6f371ff06491a0822fa", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F298cd8a12b86a6f371ff06491a0822fa.blade.php:1", "ajax": false, "filename": "298cd8a12b86a6f371ff06491a0822fa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::298cd8a12b86a6f371ff06491a0822fa"}, {"name": "1x __components::c59870a61b233f0766e3260625bdb025", "param_count": null, "params": [], "start": **********.493253, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/c59870a61b233f0766e3260625bdb025.blade.php__components::c59870a61b233f0766e3260625bdb025", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fc59870a61b233f0766e3260625bdb025.blade.php:1", "ajax": false, "filename": "c59870a61b233f0766e3260625bdb025.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c59870a61b233f0766e3260625bdb025"}, {"name": "1x __components::3890eca5d46a147ef99ddf994453d2ec", "param_count": null, "params": [], "start": **********.49517, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3890eca5d46a147ef99ddf994453d2ec.blade.php__components::3890eca5d46a147ef99ddf994453d2ec", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3890eca5d46a147ef99ddf994453d2ec.blade.php:1", "ajax": false, "filename": "3890eca5d46a147ef99ddf994453d2ec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3890eca5d46a147ef99ddf994453d2ec"}, {"name": "1x __components::ff1fde71531b073b2c658173537aaea5", "param_count": null, "params": [], "start": **********.498365, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ff1fde71531b073b2c658173537aaea5.blade.php__components::ff1fde71531b073b2c658173537aaea5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fff1fde71531b073b2c658173537aaea5.blade.php:1", "ajax": false, "filename": "ff1fde71531b073b2c658173537aaea5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff1fde71531b073b2c658173537aaea5"}, {"name": "1x __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "param_count": null, "params": [], "start": **********.50069, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php__components::5cef0de51e1489c31c7fcb5d7f2f6a97", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php:1", "ajax": false, "filename": "5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5cef0de51e1489c31c7fcb5d7f2f6a97"}, {"name": "1x __components::89cb89d3fdb0a0a12f8aa61073c231e6", "param_count": null, "params": [], "start": **********.502893, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/89cb89d3fdb0a0a12f8aa61073c231e6.blade.php__components::89cb89d3fdb0a0a12f8aa61073c231e6", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F89cb89d3fdb0a0a12f8aa61073c231e6.blade.php:1", "ajax": false, "filename": "89cb89d3fdb0a0a12f8aa61073c231e6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::89cb89d3fdb0a0a12f8aa61073c231e6"}, {"name": "1x __components::5a5b09d3f2ee0ddb2b536feb532d230a", "param_count": null, "params": [], "start": **********.505123, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5a5b09d3f2ee0ddb2b536feb532d230a.blade.php__components::5a5b09d3f2ee0ddb2b536feb532d230a", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5a5b09d3f2ee0ddb2b536feb532d230a.blade.php:1", "ajax": false, "filename": "5a5b09d3f2ee0ddb2b536feb532d230a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5a5b09d3f2ee0ddb2b536feb532d230a"}, {"name": "1x __components::fedc652debeb23dcbb31a98830baa397", "param_count": null, "params": [], "start": **********.507133, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fedc652debeb23dcbb31a98830baa397.blade.php__components::fedc652debeb23dcbb31a98830baa397", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffedc652debeb23dcbb31a98830baa397.blade.php:1", "ajax": false, "filename": "fedc652debeb23dcbb31a98830baa397.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fedc652debeb23dcbb31a98830baa397"}, {"name": "1x __components::ff3b2cf4e42e74e63db76ff05c5f2374", "param_count": null, "params": [], "start": **********.510134, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ff3b2cf4e42e74e63db76ff05c5f2374.blade.php__components::ff3b2cf4e42e74e63db76ff05c5f2374", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fff3b2cf4e42e74e63db76ff05c5f2374.blade.php:1", "ajax": false, "filename": "ff3b2cf4e42e74e63db76ff05c5f2374.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff3b2cf4e42e74e63db76ff05c5f2374"}, {"name": "1x __components::745871da7c635a3f461dfaeeef54a48e", "param_count": null, "params": [], "start": **********.513449, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/745871da7c635a3f461dfaeeef54a48e.blade.php__components::745871da7c635a3f461dfaeeef54a48e", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F745871da7c635a3f461dfaeeef54a48e.blade.php:1", "ajax": false, "filename": "745871da7c635a3f461dfaeeef54a48e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::745871da7c635a3f461dfaeeef54a48e"}, {"name": "1x __components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.517152, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php:1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2b3233eda7e50501ef45fd875b12da49"}, {"name": "1x __components::b13663c834a4ae876ef8f72aa0610e8c", "param_count": null, "params": [], "start": **********.519151, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/b13663c834a4ae876ef8f72aa0610e8c.blade.php__components::b13663c834a4ae876ef8f72aa0610e8c", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fb13663c834a4ae876ef8f72aa0610e8c.blade.php:1", "ajax": false, "filename": "b13663c834a4ae876ef8f72aa0610e8c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b13663c834a4ae876ef8f72aa0610e8c"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.519677, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php:1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.520028, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php:1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.520839, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.521177, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php:1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.522141, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php:1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.522662, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php:1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::3cec1c87224222bda738c53f782c5bc1", "param_count": null, "params": [], "start": **********.52406, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3cec1c87224222bda738c53f782c5bc1.blade.php__components::3cec1c87224222bda738c53f782c5bc1", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3cec1c87224222bda738c53f782c5bc1.blade.php:1", "ajax": false, "filename": "3cec1c87224222bda738c53f782c5bc1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3cec1c87224222bda738c53f782c5bc1"}, {"name": "1x __components::414e4e803eeec6389552bb46515583c5", "param_count": null, "params": [], "start": **********.527399, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/414e4e803eeec6389552bb46515583c5.blade.php__components::414e4e803eeec6389552bb46515583c5", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F414e4e803eeec6389552bb46515583c5.blade.php:1", "ajax": false, "filename": "414e4e803eeec6389552bb46515583c5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::414e4e803eeec6389552bb46515583c5"}, {"name": "1x __components::c47a448d99d5719cb034f7947c739ff8", "param_count": null, "params": [], "start": **********.528362, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/c47a448d99d5719cb034f7947c739ff8.blade.php__components::c47a448d99d5719cb034f7947c739ff8", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fc47a448d99d5719cb034f7947c739ff8.blade.php:1", "ajax": false, "filename": "c47a448d99d5719cb034f7947c739ff8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c47a448d99d5719cb034f7947c739ff8"}, {"name": "1x __components::e226165e1fca7eccb10f6857d7cd235a", "param_count": null, "params": [], "start": **********.529368, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/e226165e1fca7eccb10f6857d7cd235a.blade.php__components::e226165e1fca7eccb10f6857d7cd235a", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fe226165e1fca7eccb10f6857d7cd235a.blade.php:1", "ajax": false, "filename": "e226165e1fca7eccb10f6857d7cd235a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e226165e1fca7eccb10f6857d7cd235a"}, {"name": "4x a74ad8dfacd4f985eb3977517615ce25::modal", "param_count": null, "params": [], "start": **********.529804, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal.blade.phpa74ad8dfacd4f985eb3977517615ce25::modal", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php:1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 4, "name_original": "a74ad8dfacd4f985eb3977517615ce25::modal"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::custom-template", "param_count": null, "params": [], "start": **********.531329, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/custom-template.blade.phpa74ad8dfacd4f985eb3977517615ce25::custom-template", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php:1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.531891, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php:1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "4x a74ad8dfacd4f985eb3977517615ce25::modal.close-button", "param_count": null, "params": [], "start": **********.533063, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/close-button.blade.phpa74ad8dfacd4f985eb3977517615ce25::modal.close-button", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php:1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 4, "name_original": "a74ad8dfacd4f985eb3977517615ce25::modal.close-button"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::loading", "param_count": null, "params": [], "start": **********.533517, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/loading.blade.phpa74ad8dfacd4f985eb3977517615ce25::loading", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php:1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::loading"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::form.checkbox", "param_count": null, "params": [], "start": **********.536359, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.540407, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php:1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::debug-badge", "param_count": null, "params": [], "start": **********.666495, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/debug-badge.blade.phpa74ad8dfacd4f985eb3977517615ce25::debug-badge", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php:1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::debug-badge"}, {"name": "2x a74ad8dfacd4f985eb3977517615ce25::modal.action", "param_count": null, "params": [], "start": **********.667156, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/action.blade.phpa74ad8dfacd4f985eb3977517615ce25::modal.action", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php:1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 2, "name_original": "a74ad8dfacd4f985eb3977517615ce25::modal.action"}, {"name": "2x a74ad8dfacd4f985eb3977517615ce25::modal.alert", "param_count": null, "params": [], "start": **********.667925, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/alert.blade.phpa74ad8dfacd4f985eb3977517615ce25::modal.alert", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php:1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "a74ad8dfacd4f985eb3977517615ce25::modal.alert"}, {"name": "1x __components::dcf17957c4aa053a618fd9c312cc29fc", "param_count": null, "params": [], "start": **********.669701, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dcf17957c4aa053a618fd9c312cc29fc.blade.php__components::dcf17957c4aa053a618fd9c312cc29fc", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdcf17957c4aa053a618fd9c312cc29fc.blade.php:1", "ajax": false, "filename": "dcf17957c4aa053a618fd9c312cc29fc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dcf17957c4aa053a618fd9c312cc29fc"}, {"name": "1x __components::fcfb9f9ba5fb460899f38b71f491e1fe", "param_count": null, "params": [], "start": **********.67294, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fcfb9f9ba5fb460899f38b71f491e1fe.blade.php__components::fcfb9f9ba5fb460899f38b71f491e1fe", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffcfb9f9ba5fb460899f38b71f491e1fe.blade.php:1", "ajax": false, "filename": "fcfb9f9ba5fb460899f38b71f491e1fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fcfb9f9ba5fb460899f38b71f491e1fe"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::layouts.base", "param_count": null, "params": [], "start": **********.67403, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/layouts/base.blade.phpa74ad8dfacd4f985eb3977517615ce25::layouts.base", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php:1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.674869, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.686479, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.688412, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php:1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.690567, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Ffooter.blade.php:1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.691421, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php:1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00339, "accumulated_duration_str": "3.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.5436132, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 13.569}, {"sql": "select `name`, `id` from `pages` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/ThemeController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\ThemeController.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.562054, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:46", "source": {"index": 14, "namespace": null, "name": "platform/packages/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\page\\src\\Providers\\HookServiceProvider.php", "line": 46}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FProviders%2FHookServiceProvider.php:46", "ajax": false, "filename": "HookServiceProvider.php", "line": "46"}, "connection": "xmetr", "explain": null, "start_percent": 13.569, "width_percent": 15.044}, {"sql": "select `lang_id`, `lang_name`, `lang_code`, `lang_flag` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 218}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.587556, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 28.614, "width_percent": 17.109}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 81}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.590918, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 45.723, "width_percent": 12.979}, {"sql": "select `lang_id`, `lang_name`, `lang_code`, `lang_flag` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 218}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.266983, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 58.702, "width_percent": 14.454}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 81}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.269687, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 73.156, "width_percent": 12.684}, {"sql": "select count(*) as aggregate from `re_consults` where `status` = 'unread'", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 721}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.349327, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:721", "source": {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 721}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FProviders%2FHookServiceProvider.php:721", "ajax": false, "filename": "HookServiceProvider.php", "line": "721"}, "connection": "xmetr", "explain": null, "start_percent": 85.841, "width_percent": 14.159}]}, "models": {"data": {"Xmetr\\Language\\Models\\Language": {"value": 20, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Xmetr\\Page\\Models\\Page": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php:1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 26, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/theme/options/opt-text-subsection-real-estate", "action_name": "theme.options", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\ThemeController@getOptions", "uri": "GET admin/theme/options/{id?}", "controller": "Xmetr\\Theme\\Http\\Controllers\\ThemeController@getOptions<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FThemeController.php:56\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\Theme\\Http\\Controllers", "prefix": "admin/theme/options/{id?}", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FThemeController.php:56\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/ThemeController.php:56-86</a>", "middleware": "web, core, auth", "duration": "2.24s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1903934493 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1903934493\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-175957667 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-175957667\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1435053807 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">https://xmetr.gc/admin/theme/options</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; project_wishlist=25; wishlist=657; _hjSession_6417422=eyJpZCI6Ijg3Mjk5NzNiLTU1NjUtNDEyYi1hNTZlLTJlMTk4ODQzYTk2MCIsImMiOjE3NTI5NDgxNjUzNzUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1752948165$o95$g1$t1752952308$j44$l0$h0; XSRF-TOKEN=eyJpdiI6IktpODl5ZlM3MWh4eC8zWVRBUU1rQmc9PSIsInZhbHVlIjoiR2ExMFBqM2NBdkJoZis4MVBoU1dWZFRqbFU1Rkg3VDk0d2xZdFZEbm81NDJjK3p0RncxS3o5RjRQektadDZQTU5QczdZL1lIRDZGaVF4RVNqYWtVQnZVQi9PL0RwVml0czBEaG9Pd0RWYmdad1lFMEFkVGR5aUpLUWVhMXRIdjUiLCJtYWMiOiI5M2E5YTA4MjczZTE3OWE0YmZkNzI1M2I0OTcwODc1NDc1ZGE2Mjc0MDJjNDNjNGUzMGI0ZGY5Zjk1OWVjYmZlIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6ImRxTFgzNkpYamlDOTIzQS9HRlY3Z0E9PSIsInZhbHVlIjoiOUQrMnJ0OGZXSXlXYVF1OXhtck1FR24wK1MwS3BwY2E1aTl3S3BVTzRzTDhiMUF6MnpMNUxqV2NYTmUrSk50Qm9QbmQwVW00dTBPazlDbzFzOXo3dzJPSUNpS0RSaVczd0tEL3pkUXo5YlZmN0lQQ3BYaTFYYnJ0UG1uQjBJek0iLCJtYWMiOiI3MDM5Y2JhMmEwYjQwMWUzMWRkODIwMzU0ZDVjMGYwYjNlYzVhNjYyMDlhM2QxNjFjMWUwOWIwZDZjZmZiODRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435053807\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-269203257 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IXGjHW0EqltPz3rfutxdkjYejhyX2AuH8ZqUYqdC</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ao5wIXd5JG8FpH7TPxAaB6WV42m94erVNEyHw9Z4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269203257\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1389419933 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 19:18:29 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389419933\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IXGjHW0EqltPz3rfutxdkjYejhyX2AuH8ZqUYqdC</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">https://xmetr.gc/admin/theme/options</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>1752949852</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>101</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>101</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/theme/options/opt-text-subsection-real-estate", "action_name": "theme.options", "controller_action": "Xmetr\\Theme\\Http\\Controllers\\ThemeController@getOptions"}, "badge": null}}