

<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['grid grid-cols-3 gap-y-[20px] gap-x-[16px] max-[1024px]:grid-cols-2', $class ?? null]); ?>">

    
    <?php if($property->categories->isNotEmpty()): ?>
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">🏘</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold"><?php echo e(__('Type')); ?></p>
            <p class="text-black text-[15px]"><?php echo e(implode(', ', $property->categories->map(function($category) { return $category->name; })->toArray())); ?></p>
            </div>
        </div>
    <?php endif; ?>


    

    <?php if($property->square): ?>
    <div class="flex items-center gap-[10px]">
        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
          <p class="text-[25px]">📐</p>
        </div>

        <div class="flex flex-col">
          <p class="text-black text-[15px] font-bold"><?php echo e(__('Area м²')); ?></p>
          <p class="text-black text-[15px]"><?php echo e($property->square_text); ?></p>
        </div>
      </div>
    <?php endif; ?>

    <?php if($property->number_floor): ?>
    <div class="flex items-center gap-[10px]">
        <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
          <p class="text-[25px]">🚪</p>
        </div>

        <div class="flex flex-col">
          <p class="text-black text-[15px] font-bold"><?php echo e(__('Floors')); ?></p>
          <p class="text-black text-[15px]"><?php echo e(number_format($property->number_floor)); ?></p>
        </div>
      </div>

    <?php endif; ?>

    <?php if($property->number_bedroom): ?>
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">🛌</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold"><?php echo e(__('Bedrooms')); ?></p>
            <p class="text-black text-[15px]"><?php echo e(number_format($property->number_bedroom)); ?></p>
            </div>
        </div>
    <?php endif; ?>
    <?php if($property->number_bathroom): ?>
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">🛀</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold"><?php echo e(__('Bathrooms')); ?></p>
            <p class="text-black text-[15px]"><?php echo e(number_format($property->number_bathroom)); ?></p>
            </div>
        </div>
    <?php endif; ?>
    <?php if($property->district_id): ?>
        <div class="flex items-center gap-[10px]">
            <div class="w-[50px] h-[50px] bg-white rounded-[10px] border border-[#D6D6D7] flex justify-center items-center">
            <p class="text-[25px]">📶</p>
            </div>

            <div class="flex flex-col">
            <p class="text-black text-[15px] font-bold"><?php echo e(__('District Ratings')); ?></p>
            <p class="text-black text-[15px]"><?php echo e(number_format($property->district->average_rating,1)); ?> / 5</p>
            </div>
        </div>
    <?php endif; ?>



</div>
<span class="w-full h-[1px] block bg-[#D6D6D7]"></span>

<?php if($property->content || $property->private_notes || $property->original_description): ?>

    <?php if($property->content): ?>
        <h4 class="title"><?php echo e(__('Description')); ?></h4>
          <?php if($property->original_description): ?>
          <p><?php echo e(__('This text translated')); ?> <button id="toggleButton" class="text-[#5E2DC2] font-bold underline"><?php echo e(__('Show Original')); ?></button></p>
           <div id="originalDescription" style="display: none;">
                <?php echo BaseHelper::clean($property->original_description); ?>

           </div>

    <style>
    #originalDescription {
        border: 1px solid #e9e9e9;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 10px;
    }
    #originalDescription p {
        margin-bottom: 0;
    }
  </style>
    <script>
    const toggleBtn = document.getElementById("toggleButton");
    const descriptionBox = document.getElementById("originalDescription");

    toggleBtn.addEventListener("click", function () {
      if (descriptionBox.style.display === "none") {
        descriptionBox.style.display = "block";
        toggleBtn.textContent = "<?php echo e(__('Close')); ?>";
      } else {
        descriptionBox.style.display = "none";
        toggleBtn.textContent = "<?php echo e(__('Show Original')); ?>";
      }
    });
  </script>
            <?php endif; ?>
        <div class="text-black text-[15px]">
            <?php echo BaseHelper::clean($property->content); ?>

        </div>
    <?php endif; ?>

    <?php if($property->can_see_private_notes && $property->private_notes): ?>
        <h4 class="title"><?php echo e(__('Private Notes')); ?></h4>
        <div class="text-black text-[15px]">
        <?php echo BaseHelper::clean(nl2br($property->private_notes)); ?>

        </div>
    <?php endif; ?>

<span class="w-full h-[1px] block bg-[#D6D6D7]"></span>
<?php endif; ?>


<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/single-layouts/partials/description.blade.php ENDPATH**/ ?>