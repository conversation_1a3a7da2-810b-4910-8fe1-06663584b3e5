<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['homeya-box', $class ?? null]); ?>" <?php if($project->latitude && $project->longitude): ?> data-lat="<?php echo e($project->latitude); ?>" data-lng="<?php echo e($project->longitude); ?>" <?php endif; ?>>
    <div class="archive-top">
        <a href="<?php echo e($project->url); ?>" class="images-group">
            <div class="images-style">
                <?php echo e(RvMedia::image($project->image, $project->name, 'medium-rectangle')); ?>

            </div>
            <div class="top">
                <div class="d-flex gap-8">
                    <?php if($project->is_featured): ?>
                        <span class="flag-tag success"><?php echo e(__('Featured')); ?></span>
                    <?php endif; ?>
                </div>
                <?php if(RealEstateHelper::isEnabledWishlist()): ?>
                    <button type="button" class="box-icon w-32"
                            data-type="project"
                            data-bb-toggle="add-to-wishlist"
                            data-id="<?php echo e($project->getKey()); ?>"
                            data-add-message="<?php echo e(__('Added ":name" to wishlist successfully!', ['name' => $project->name])); ?>"
                            data-remove-message="<?php echo e(__('Removed ":name" from wishlist successfully!', ['name' => $project->name])); ?>"
                    >
                        <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-heart'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                    </button>
                <?php endif; ?>
            </div>
            <?php if($project->category): ?>
                <div class="bottom">
                    <span class="flag-tag style-2"><?php echo e($project->category->name); ?></span>
                </div>
            <?php endif; ?>
        </a>
        <div class="content">
            <div class="text-capitalize h7 fw-7">
                <a href="<?php echo e($project->url); ?>" class="link line-clamp-1" title="<?php echo e($project->name); ?>"><?php echo BaseHelper::clean($project->name); ?></a>
            </div>
            <?php if($project->short_address): ?>
                <div class="desc">
                    <i class="icon icon-mapPin"></i>
                    <p class="line-clamp-1"><?php echo e($project->short_address); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="archive-bottom d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <h6><?php echo e($project->formatted_price); ?></h6>
        </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/projects/item-grid.blade.php ENDPATH**/ ?>