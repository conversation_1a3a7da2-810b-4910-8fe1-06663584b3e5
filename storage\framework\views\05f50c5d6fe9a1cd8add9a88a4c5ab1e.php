<?php if(theme_option('real_estate_use_location_in_search_box_as_dropdown', 'no') === 'yes'): ?>
    <?php
        $cities = \Xmetr\Location\Models\City::query()
            ->wherePublished()
            ->select('name', 'id')
            ->orderBy('name')
            ->get();
    ?>

    <div class="form-group-2 form-style">
        <label><?php echo e(__('Location')); ?></label>
        <div class="position-relative">
            <div class="group-select">
                <select name="city_id" id="location" class="select_js">
                    <option value=""><?php echo e(__('All')); ?></option>
                    <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($city->getKey()); ?>"<?php if(request()->query('city_id') == $city->getKey()): echo 'selected'; endif; ?>><?php echo e($city->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="form-group-2 form-style" data-bb-toggle="search-suggestion">
        <label><?php echo e(__('Location')); ?></label>
        <div class="position-relative">
            <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['group-ip', 'ip-icon' => $style === 3]); ?>">
                <input type="text" class="form-control" placeholder="<?php echo e(__('Search for Location')); ?>" value="<?php echo e(BaseHelper::stringify(request()->query('location'))); ?>" name="location" data-url="<?php echo e(route('public.ajax.cities')); ?>" />
                <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-current-location'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses(['icon-right icon-location' => $style === 3]))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
            </div>
            <div data-bb-toggle="data-suggestion"></div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/filters/location.blade.php ENDPATH**/ ?>