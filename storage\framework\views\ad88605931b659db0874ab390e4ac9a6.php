<div class="wrapper ovh pt-[20px] pb-[40px]">
    <div class="body_content container flex flex-col gap-[25px]">
        <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.gallery-grid'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="flex justify-between items-start gap-[24px] max-[1280px]:flex-col">
            <div class="flex flex-col gap-[25px]">
                <div class="flex items-center gap-[20px] flex-wrap">
                    <?php if($commission = $property->getMetaData('commission', true)): ?>
                        <div class="bg-[#FBF0EE] px-[8px] py-[5px] rounded-[5px]">
                        <p class="text-[#DC6F5A] text-[13px]"><?php echo e(__('Commission')); ?> <?php echo e($commission); ?></p>
                        </div>
                    <?php endif; ?>

                    <p class="text-[13px] text-black">🕒 <?php echo e(Theme::formatDate($property->created_at)); ?></p>
                    <?php if(setting('real_estate_display_views_count_in_detail_page', true)): ?>
                    <p class="text-[13px] text-black">
                        👁️ <?php echo e($property->views_text); ?>

                    </p>
                    <?php endif; ?>
                </div>


                    <h3 class="title"><?php echo e($property->name); ?></h3>


                    <div class="flex items-center flex-wrap gap-[5px]">
                        <?php if($property->city): ?>
                        <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                        <p class="text-[13px] text-[#717171]"><?php echo e($property->city->name); ?></p>
                        </div>
                        <?php endif; ?>
                        <?php if($deposit = $property->getMetaData('deposit', true)): ?>
                        <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                            <p class="text-[13px] text-[#717171]"><?php echo e(__('Deposit')); ?> : <?php echo e(format_price((float)$deposit, $property->currency)); ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if($property->bills_included): ?>
                        <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                            <p class="text-[13px] text-[#717171]"><?php echo e(__('Bills Included')); ?></p>
                        </div>
                        <?php endif; ?>
                    <?php if(!empty($property->suitable_for_labels)): ?>
                        <?php $__currentLoopData = $property->suitable_for_labels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $suitableLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                            <p class="text-[13px] text-[#717171]"><?php echo e($suitableLabel); ?></p>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                    </div>



                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.description'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    

                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.features'), ['class' => 'single-property-element'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.facilities'), ['class' => 'single-property-element'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.video-gallery'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.rental-conditions'), ['class' => 'single-property-element'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.district-ratings'), ['class' => 'single-property-element'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <h4 class="title" id="apartamentsAdress"><?php echo e(__('Location')); ?></h4>
                    <span id="apartamentsMapAnchor" class="invisible absolute"></span>
                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.map'), ['class' => 'single-property-element'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <div>
                        <h4 class="title" id="apartamentsAdress"><?php echo e(__('Address')); ?></h4>
                    <p class="text-[15px] text-black"><?php echo e($property->location ?: $property->short_address); ?></p>

                    </div>

                    <div class="grid grid-cols-2 gap-[20px] max-[768px]:grid-cols-1">

                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.project'), ['class' => 'single-property-element'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


                    </div>
            </div>

                <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.agent'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <!-- Sidebar -->

                    



                    

                    

                    
        </div>



        <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.single-layouts.partials.related-properties'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

         <!-- Menu -->
        <div class="fixed left-0 bottom-0 w-full hidden max-[768px]:flex gap-[10px] items-stretch justify-between px-[20px] py-[30px] z-[3]">
            <a href="#apartamentsMapAnchor" class="shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[10px] px-[20px] w-[50px] h-[50px] z-[4]">
            <svg class="shrink-0" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.72604 1.60466V13.4768M13.274 3.99746V16.2166M1 5.05669V13.9425C1 15.6777 2.23288 16.39 3.73059 15.5316L5.87671 14.3078C6.34246 14.043 7.11872 14.0156 7.60273 14.2622L12.3973 16.664C12.8813 16.9014 13.6575 16.8832 14.1233 16.6183L18.0776 14.3535C18.5799 14.0613 19 13.3489 19 12.7645V3.87861C19 2.14346 17.7671 1.43113 16.2694 2.28957L14.1233 3.51332C13.6575 3.77816 12.8813 3.80555 12.3973 3.55898L7.60273 1.16629C7.11872 0.928845 6.34246 0.94711 5.87671 1.21195L1.92237 3.47679C1.41096 3.76902 1 4.48135 1 5.05669Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            </a>

            <a href="#apartamentsMapAnchor" class="bg-[#5E2DC2] px-[35px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex items-center justify-center gap-[10px] relative shrink-0 z-[4] w-fit">
            <p class="text-white text-[15px] font-bold"><?php echo e(__('Contact')); ?></p>
            </a>

            

            <button type="button" class="x-favorite shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[10px] px-[20px] z-[4] w-[50px] h-[50px] relative"
                        data-type="property"
                        data-bb-toggle="add-to-wishlist"
                        data-id="<?php echo e($property->getKey()); ?>"
                        data-add-message="<?php echo e(__('Added ":name" to wishlist successfully!', ['name' => $property->name])); ?>"
                        data-remove-message="<?php echo e(__('Removed ":name" from wishlist successfully!', ['name' => $property->name])); ?>"
                >
                <svg class="x-favorite_icon x-favorite-notFilled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="white"></path>
                </svg>
                <svg class="x-favorite_icon x-favorite-filled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2 3.5L5.5 0.5L10 4.5L14 0.5L18.5 3L19.5 5L18 9.5L15 13L14 14.5L10 17.5L6.5 14L3.5 12L1 8.5L2 3.5Z" fill="#FF0000"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="#FF0000"></path>
                </svg>
            </button>

            <div class="absolute bottom-0 left-0 w-full h-[95px]" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
        </div>

    </div>
</div>


<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/single-layouts/style-5.blade.php ENDPATH**/ ?>