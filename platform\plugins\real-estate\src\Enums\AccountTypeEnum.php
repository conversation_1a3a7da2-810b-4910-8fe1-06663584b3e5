<?php

namespace Xmetr\RealEstate\Enums;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static AccountTypeEnum OWNER()
 * @method static AccountTypeEnum AGENT()
 * @method static AccountTypeEnum DEVELOPER()
 */
class AccountTypeEnum extends Enum
{
    public const OWNER = 'owner';

    public const AGENT = 'agent';

    public const DEVELOPER = 'developer';

    public static $langPath = 'plugins/real-estate::account.types';

    public function toHtml(): HtmlString|string|null
    {
        $color = match ($this->value) {
            self::OWNER => 'success',
            self::AGENT => 'info',
            self::DEVELOPER => 'warning',
            default => 'primary',
        };

        return BaseHelper::renderBadge($this->label(), $color);
    }
}
