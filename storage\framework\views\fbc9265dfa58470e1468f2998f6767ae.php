<?php
    $backgroundColor = Theme::get('breadcrumbBackgroundColor') ?? theme_option('breadcrumb_background_color', '#f7f7f7');
    $textColor = Theme::get('breadcrumbTextColor') ?? theme_option('breadcrumb_text_color', '#161e2d');
    $backgroundImage = Theme::get('breadcrumbBackgroundImage') ?? (theme_option('breadcrumb_background_image') ? RvMedia::getImageUrl(theme_option('breadcrumb_background_image')) : null);

    $showBreadcrumb = Theme::get('breadcrumbEnabled', 'yes');
?>

<?php if($showBreadcrumb === 'yes'): ?>
    <section class="flat-title-page style-2" style="<?php echo \Illuminate\Support\Arr::toCssStyles(["background-color: $backgroundColor" => $backgroundColor != 'transparent', "background-image: url($backgroundImage); background-size: cover; background-position: center" => $backgroundImage]) ?>">
        <div class="container">
            <ul class="breadcrumb">
                <?php $__currentLoopData = Theme::breadcrumb()->getCrumbs(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $crumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <?php if($loop->last): ?>
                            <?php echo BaseHelper::clean($crumb['label']); ?>

                        <?php else: ?>
                            <a href="<?php echo e($crumb['url']); ?>" style="<?php echo \Illuminate\Support\Arr::toCssStyles(["color: $textColor" => $textColor != 'transparent']) ?>"><?php echo BaseHelper::clean($crumb['label']); ?></a>
                            <span class="ms-1" style="<?php echo \Illuminate\Support\Arr::toCssStyles(["color: $textColor" => $textColor != 'transparent']) ?>">/</span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            
        </div>
    </section>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/partials/breadcrumb.blade.php ENDPATH**/ ?>