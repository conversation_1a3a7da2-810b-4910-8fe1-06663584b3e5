<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['wd-find-select' =>  in_array($style, [1, 2, 4]), 'wd-filter-select' => $style === 3, 'style-2 shadow-st' => $style === 2, 'no-left-round' => $noLeftRound ?? false]); ?>">
    <div class="inner-group">
        <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.keyword'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.location'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.filters.categories'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['form-group-4 box-filter', 'form-style' => $style === 3]); ?>">
            <a class="filter-advanced pull-right" href="#">
                <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-adjustments-alt'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                <span class="<?php echo \Illuminate\Support\Arr::toCssClasses(['text-1' => $style !== 3, 'text-advanced fw-7' => $style === 3]); ?>"><?php echo e(__('Advanced')); ?></span>
            </a>
        </div>
    </div>
    <?php if($style === 3): ?>
        <div class="form-style">
    <?php endif; ?>
    <button type="submit" class="tf-btn primary"><?php echo e(__('Search')); ?></button>
    <?php if($style === 3): ?>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/filters/base.blade.php ENDPATH**/ ?>