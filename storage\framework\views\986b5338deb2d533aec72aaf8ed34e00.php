<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['box-title-listing', $class ?? null]); ?>">
    <div data-bb-toggle="pagination-info"></div>
    <div class="box-filter-tab justify-content-between">
        <a href="<?php echo e($actionUrl); ?>" class="reset-filter-btn" style="<?php echo \Illuminate\Support\Arr::toCssStyles(['display: none' => empty(request()->query())]) ?>">
            <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-refresh'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
            <?php echo e(__('Reset')); ?>

        </a>
        <ul class="nav-tab-filter w-auto" role="tablist">
            <li class="nav-tab-item" role="presentation">
                <button class="<?php echo \Illuminate\Support\Arr::toCssClasses(['nav-link-item', 'active' => $itemLayout === 'grid']); ?>" data-bs-toggle="tab" data-bb-toggle="change-layout" data-value="grid">
                    <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-layout-grid'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                </button>
            </li>
            <li class="nav-tab-item" role="presentation">
                <button class="<?php echo \Illuminate\Support\Arr::toCssClasses(['nav-link-item', 'active' => $itemLayout === 'list']); ?>" data-bs-toggle="tab" data-bb-toggle="change-layout" data-value="list">
                    <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-layout-list'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                </button>
            </li>
        </ul>
        <div class="d-flex gap-2">
            <button type="button" class="btn-filter-mobile" title="<?php echo e(__('Filter')); ?>" data-bb-toggle="toggle-filter-offcanvas">
                <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-filter'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                <span class="sr-only"><?php echo e(__('Filter')); ?></span>
            </button>
            <select name="per_page" id="per_page" class="list-page select_js" data-default="12">
                <?php $__currentLoopData = $perPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $perPage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($perPage); ?>" <?php if(BaseHelper::stringify(request()->integer('per_page', 12)) == $perPage): echo 'selected'; endif; ?>><?php echo e($perPage); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            <select name="sort_by" id="sort_by" class="list-sort select_js">
                <option value=""><?php echo e(__('Default')); ?></option>
                <?php $__currentLoopData = RealEstateHelper::getSortByList(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $sortBy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($key); ?>" <?php if(BaseHelper::stringify(request()->query('sort_by')) === $key): echo 'selected'; endif; ?>><?php echo e($sortBy); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/listing-top.blade.php ENDPATH**/ ?>