{"__meta": {"id": "01K0HXQHTV6T5P17G35KWEZBD3", "datetime": "2025-07-19 18:04:22", "utime": **********.748178, "method": "GET", "uri": "/admin/audit-logs/widgets/activities", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752948258.082212, "end": **********.748207, "duration": 4.665995121002197, "duration_str": "4.67s", "measures": [{"label": "Booting", "start": 1752948258.082212, "relative_start": 0, "end": **********.432895, "relative_end": **********.432895, "duration": 1.****************, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.432934, "relative_start": 1.***************, "end": **********.748211, "relative_end": 3.814697265625e-06, "duration": 3.***************, "duration_str": "3.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.466823, "relative_start": 1.****************, "end": **********.500657, "relative_end": **********.500657, "duration": 0.033833980560302734, "duration_str": "33.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/audit-log::widgets.activities", "start": **********.60897, "relative_start": 1.****************, "end": **********.60897, "relative_end": **********.60897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.113883, "relative_start": 3.****************, "end": **********.113883, "relative_end": **********.113883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.583676, "relative_start": 3.5014641284942627, "end": **********.583676, "relative_end": **********.583676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.584407, "relative_start": 3.502195119857788, "end": **********.584407, "relative_end": **********.584407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.584929, "relative_start": 3.5027170181274414, "end": **********.584929, "relative_end": **********.584929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.589299, "relative_start": 3.507086992263794, "end": **********.589299, "relative_end": **********.589299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.589864, "relative_start": 3.5076520442962646, "end": **********.589864, "relative_end": **********.589864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.590286, "relative_start": 3.5080740451812744, "end": **********.590286, "relative_end": **********.590286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.594861, "relative_start": 3.5126490592956543, "end": **********.594861, "relative_end": **********.594861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.595447, "relative_start": 3.513235092163086, "end": **********.595447, "relative_end": **********.595447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.595871, "relative_start": 3.5136590003967285, "end": **********.595871, "relative_end": **********.595871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.600033, "relative_start": 3.5178210735321045, "end": **********.600033, "relative_end": **********.600033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.600538, "relative_start": 3.5183260440826416, "end": **********.600538, "relative_end": **********.600538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.600917, "relative_start": 3.518705129623413, "end": **********.600917, "relative_end": **********.600917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.60491, "relative_start": 3.522697925567627, "end": **********.60491, "relative_end": **********.60491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.605408, "relative_start": 3.52319598197937, "end": **********.605408, "relative_end": **********.605408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.605814, "relative_start": 3.52360200881958, "end": **********.605814, "relative_end": **********.605814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.609767, "relative_start": 3.527554988861084, "end": **********.609767, "relative_end": **********.609767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.610249, "relative_start": 3.5280370712280273, "end": **********.610249, "relative_end": **********.610249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.610554, "relative_start": 3.5283420085906982, "end": **********.610554, "relative_end": **********.610554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.613326, "relative_start": 3.531114101409912, "end": **********.613326, "relative_end": **********.613326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.613694, "relative_start": 3.531481981277466, "end": **********.613694, "relative_end": **********.613694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.613983, "relative_start": 3.531770944595337, "end": **********.613983, "relative_end": **********.613983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.616735, "relative_start": 3.5345230102539062, "end": **********.616735, "relative_end": **********.616735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.617097, "relative_start": 3.5348849296569824, "end": **********.617097, "relative_end": **********.617097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.617411, "relative_start": 3.535198926925659, "end": **********.617411, "relative_end": **********.617411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.620775, "relative_start": 3.5385630130767822, "end": **********.620775, "relative_end": **********.620775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.621345, "relative_start": 3.539133071899414, "end": **********.621345, "relative_end": **********.621345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/audit-log::activity-line", "start": **********.621759, "relative_start": 3.5395469665527344, "end": **********.621759, "relative_end": **********.621759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "start": **********.626282, "relative_start": 3.54407000541687, "end": **********.626282, "relative_end": **********.626282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.row", "start": **********.626741, "relative_start": 3.5445289611816406, "end": **********.626741, "relative_end": **********.626741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.body.index", "start": **********.627281, "relative_start": 3.5450689792633057, "end": **********.627281, "relative_end": **********.627281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::table.index", "start": **********.627917, "relative_start": 3.5457050800323486, "end": **********.627917, "relative_end": **********.627917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.simple-pagination", "start": **********.69548, "relative_start": 3.6132681369781494, "end": **********.69548, "relative_end": **********.69548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b4c13ee47c8273ac628dba702f336180", "start": **********.560226, "relative_start": 4.47801399230957, "end": **********.560226, "relative_end": **********.560226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98abfcd8227e681cb9ad72e7d1720a64", "start": **********.628411, "relative_start": 4.546199083328247, "end": **********.628411, "relative_end": **********.628411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.footer.index", "start": **********.679057, "relative_start": 4.596844911575317, "end": **********.679057, "relative_end": **********.679057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.742132, "relative_start": 4.65991997718811, "end": **********.744917, "relative_end": **********.744917, "duration": 0.0027849674224853516, "duration_str": "2.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 44587360, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 37, "nb_templates": 37, "templates": [{"name": "plugins/audit-log::widgets.activities", "param_count": null, "params": [], "start": **********.608908, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/widgets/activities.blade.phpplugins/audit-log::widgets.activities", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Fwidgets%2Factivities.blade.php:1", "ajax": false, "filename": "activities.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.113831, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.58362, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.58437, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.584897, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.589254, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.589831, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.590252, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.594817, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.595411, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.595838, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.599992, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.600506, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.600885, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.60486, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.605375, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.605782, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.609717, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.610228, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.610534, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.613301, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.613675, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.613962, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.616708, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.617074, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.61739, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.62073, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.621312, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "plugins/audit-log::activity-line", "param_count": null, "params": [], "start": **********.621726, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/views/activity-line.blade.phpplugins/audit-log::activity-line", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fresources%2Fviews%2Factivity-line.blade.php:1", "ajax": false, "filename": "activity-line.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.cell", "param_count": null, "params": [], "start": **********.626246, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/cell.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.cell", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php:1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.row", "param_count": null, "params": [], "start": **********.626712, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/row.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.row", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php:1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.body.index", "param_count": null, "params": [], "start": **********.627252, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/body/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.body.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::table.index", "param_count": null, "params": [], "start": **********.627891, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/table/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::table.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "core/base::components.simple-pagination", "param_count": null, "params": [], "start": **********.695435, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/simple-pagination.blade.phpcore/base::components.simple-pagination", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fsimple-pagination.blade.php:1", "ajax": false, "filename": "simple-pagination.blade.php", "line": "?"}}, {"name": "__components::b4c13ee47c8273ac628dba702f336180", "param_count": null, "params": [], "start": **********.56017, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/b4c13ee47c8273ac628dba702f336180.blade.php__components::b4c13ee47c8273ac628dba702f336180", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fb4c13ee47c8273ac628dba702f336180.blade.php:1", "ajax": false, "filename": "b4c13ee47c8273ac628dba702f336180.blade.php", "line": "?"}}, {"name": "__components::98abfcd8227e681cb9ad72e7d1720a64", "param_count": null, "params": [], "start": **********.628368, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98abfcd8227e681cb9ad72e7d1720a64.blade.php__components::98abfcd8227e681cb9ad72e7d1720a64", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98abfcd8227e681cb9ad72e7d1720a64.blade.php:1", "ajax": false, "filename": "98abfcd8227e681cb9ad72e7d1720a64.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::card.footer.index", "param_count": null, "params": [], "start": **********.679028, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/footer/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.footer.index", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ffooter%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05147, "accumulated_duration_str": "51.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.522492, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 5.071}, {"sql": "select count(*) as aggregate from `audit_histories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.54198, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "AuditLogController.php:22", "source": {"index": 16, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FHttp%2FControllers%2FAuditLogController.php:22", "ajax": false, "filename": "AuditLogController.php", "line": "22"}, "connection": "xmetr", "explain": null, "start_percent": 5.071, "width_percent": 4.741}, {"sql": "select * from `audit_histories` order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5477462, "duration": 0.00976, "duration_str": "9.76ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 9.812, "width_percent": 18.963}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.562993, "duration": 0.03666, "duration_str": "36.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 28.774, "width_percent": 71.226}]}, "models": {"data": {"Xmetr\\AuditLog\\Models\\AuditHistory": {"value": 10, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FModels%2FAuditHistory.php:1", "ajax": false, "filename": "AuditHistory.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 12, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/audit-logs/widgets/activities", "action_name": "audit-log.widget.activities", "controller_action": "Xmetr\\AuditLog\\Http\\Controllers\\AuditLogController@getWidgetActivities", "uri": "GET admin/audit-logs/widgets/activities", "permission": "audit-log.index", "controller": "Xmetr\\AuditLog\\Http\\Controllers\\AuditLogController@getWidgetActivities<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FHttp%2FControllers%2FAuditLogController.php:14\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\AuditLog\\Http\\Controllers", "prefix": "admin/audit-logs", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FHttp%2FControllers%2FAuditLogController.php:14\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/audit-log/src/Http/Controllers/AuditLogController.php:14-27</a>", "middleware": "web, core, auth", "duration": "4.66s", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-155206838 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-155206838\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1622729501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1622729501\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1858522552 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZHMGw1ZTFjSzhRb0N6QjZHWmxOOWc9PSIsInZhbHVlIjoiekR3NitlazZ5dmFvdWRJcUwyRm80TUdnTmsxdlI0N1pqVUkwdFVzRzdaMUoxODJhem5TaStLWFRja1RvZlNpNXZRanpuWldzb1MzRE5yYlZsYkphemhIWWFZdUtucXFGbjdHYkVrU2o0Ty9CVEZJWDM0OUFXU0E5OXdLN3U3QTYiLCJtYWMiOiJhZmI5OWJjYWEyNDg0Yzc4NmNiY2M4MzQzMWE5NzNkNmFiMTdiYWJmMWQxN2Q4NmRkZjI2NmM3Y2YwYmViZTFkIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">https://xmetr.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; project_wishlist=25; wishlist=657; _hjSession_6417422=eyJpZCI6Ijg3Mjk5NzNiLTU1NjUtNDEyYi1hNTZlLTJlMTk4ODQzYTk2MCIsImMiOjE3NTI5NDgxNjUzNzUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; XSRF-TOKEN=eyJpdiI6IjZHMGw1ZTFjSzhRb0N6QjZHWmxOOWc9PSIsInZhbHVlIjoiekR3NitlazZ5dmFvdWRJcUwyRm80TUdnTmsxdlI0N1pqVUkwdFVzRzdaMUoxODJhem5TaStLWFRja1RvZlNpNXZRanpuWldzb1MzRE5yYlZsYkphemhIWWFZdUtucXFGbjdHYkVrU2o0Ty9CVEZJWDM0OUFXU0E5OXdLN3U3QTYiLCJtYWMiOiJhZmI5OWJjYWEyNDg0Yzc4NmNiY2M4MzQzMWE5NzNkNmFiMTdiYWJmMWQxN2Q4NmRkZjI2NmM3Y2YwYmViZTFkIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IkhVZmxTM3VWeUdjZ0tEZGxCZVpuaHc9PSIsInZhbHVlIjoiWklvSWc5TExCTDVFQUMwZnh3WkNjaGRXekZiSmtRZlhzV2d4T3ZlM3lHRjdVWThoM1BNdnh3ZUVZRkYwQ1dpN3lhMm0xK0RWWmREbVVNL2xNckhhd0krelB4aGNTbG83dFV4QmFuOXc5VUo2aFpVSlAyVVhLS2xjTmJuNlZiOXYiLCJtYWMiOiI2NDBmMjlmMDgyOTBlYThkM2M4YTIyMTRjOGZlZTNjNTE3MDRhYWI0NzAzNjk5MDY4NWZjOWJjNTZjYmZmMTI1IiwidGFnIjoiIn0%3D; _ga_KQ6X76DET4=GS2.1.s1752948165$o95$g0$t1752948255$j60$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858522552\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1913448543 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RobK1zu3t1m8xEzREPlVbKwr1yqlht8JLepj3TJp</span>\"\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913448543\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1177279302 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 18:04:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177279302\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1308712529 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">https://xmetr.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308712529\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/audit-logs/widgets/activities", "action_name": "audit-log.widget.activities", "controller_action": "Xmetr\\AuditLog\\Http\\Controllers\\AuditLogController@getWidgetActivities"}, "badge": null}}