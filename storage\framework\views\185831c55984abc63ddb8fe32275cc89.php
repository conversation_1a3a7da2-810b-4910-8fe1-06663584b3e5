<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['widget-price', $class ?? null]); ?>" data-bb-toggle="range" data-min="0" data-max="<?php echo e(get_max_properties_price()); ?>">
    <div class="box-title-price">
        <span class="title-price"><?php echo e(__('Price Range')); ?></span>
        <div class="caption-price">
            <span><?php echo e(__('from')); ?></span>
            <span data-bb-toggle="range-from-value" class="fw-7 ms-1 me-1"></span>
            <span><?php echo e(__('to')); ?></span>
            <span data-bb-toggle="range-to-value" class="fw-7 ms-1"></span>
        </div>
    </div>
    <div data-bb-toggle="range-slider" data-currency-prefix-symbol="<?php echo e(get_application_currency()->is_prefix_symbol); ?>" data-currency-symbol="<?php echo e(get_application_currency()->symbol); ?>" data-currency-with-space="<?php echo e((setting('real_estate_add_space_between_price_and_currency', 0) == 1)); ?>"></div>
    <div class="slider-labels">
        <div>
            <input type="hidden" data-bb-toggle="min-input" name="min_price" value="<?php echo e(BaseHelper::stringify(request()->float('min_price'))); ?>" />
            <input type="hidden" data-bb-toggle="max-input" name="max_price" value="<?php echo e(BaseHelper::stringify(request()->float('max_price'))); ?>" />
        </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/filters/price.blade.php ENDPATH**/ ?>