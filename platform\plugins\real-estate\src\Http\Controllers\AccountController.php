<?php

namespace Xmetr\RealEstate\Http\Controllers;

use Xmetr\Base\Events\UpdatedContentEvent;
use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Http\Actions\DeleteResourceAction;
use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Media\Models\MediaFile;
use Xmetr\Optimize\Facades\OptimizerHelper;
use Xmetr\RealEstate\Forms\AccountForm;
use Xmetr\RealEstate\Http\Requests\AccountCreateRequest;
use Xmetr\RealEstate\Http\Requests\AccountEditRequest;
use Xmetr\RealEstate\Http\Resources\AccountResource;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Tables\AccountTable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Xmetr\RealEstate\Enums\AccountTypeEnum;
use Xmetr\RealEstate\Http\Resources\AccountResourceSelect2;

class AccountController extends BaseController
{
    public function __construct()
    {
        OptimizerHelper::disable();

        $this
            ->breadcrumb()
            ->add(trans('plugins/real-estate::account.name'), route('account.index'));
    }

    public function index(AccountTable $dataTable)
    {
        $this->pageTitle(trans('plugins/real-estate::account.name'));

        return $dataTable->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/real-estate::account.create'));

        return AccountForm::create()
            ->remove('is_change_password')
            ->renderForm();
    }

    public function store(AccountCreateRequest $request)
    {
        $form = AccountForm::create();

        $form
            ->saving(function (AccountForm $form) use ($request): void {
                $account = $form->getModel();

                $account->fill($request->except('password'));

                $account->is_featured = $request->input('is_featured');
                $account->is_public_profile = $request->input('is_public_profile', 0);
                $account->confirmed_at = Carbon::now();
                $account->approved_at = Carbon::now();

                $account->password = Hash::make($request->input('password'));
                $account->dob = Carbon::parse($request->input('dob'))->toDateString();

                if ($avatarImage = $request->input('avatar_image')) {
                    $account->avatar_id = MediaFile::query()
                        ->where('url', $avatarImage)
                        ->value('id');
                } else {
                    $account->avatar_id = null;
                }

                $account->save();

                // Sync spoken languages
                $spokenLanguages = $request->input('spoken_languages', []);

                // Ensure we have a proper array and filter out invalid values
                if (is_array($spokenLanguages)) {
                    $spokenLanguages = array_filter($spokenLanguages, function($value) {
                        return !empty($value) && is_numeric($value) && $value > 0;
                    });
                    // Re-index the array to avoid issues with sync
                    $spokenLanguages = array_values($spokenLanguages);
                } else {
                    $spokenLanguages = [];
                }

                $account->spokenLanguages()->sync($spokenLanguages);
            });

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('account.index'))
            ->setNextUrl(route('account.edit', $form->getModel()->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(Account $account)
    {
        $this->pageTitle(trans('plugins/real-estate::account.edit', ['name' => $account->name]));

        $account->password = null;

        return AccountForm::createFromModel($account)
            ->renderForm();
    }

    public function update(Account $account, AccountEditRequest $request)
    {
        $form = AccountForm::createFromModel($account)->setRequest($request);

        $form
            ->saving(function (AccountForm $form) use ($request): void {
                $account = $form->getModel();

                $account->fill($request->except('password'));

                if ($request->input('is_change_password') == 1) {
                    $account->password = Hash::make($request->input('password'));
                }

                $account->dob = Carbon::parse($request->input('dob'))->toDateString();

                if ($avatarImage = $request->input('avatar_image')) {
                    $account->avatar_id = MediaFile::query()
                        ->where('url', $avatarImage)
                        ->value('id');
                } else {
                    $account->avatar_id = null;
                }

                $account->is_featured = $request->input('is_featured');
                $account->is_public_profile = $request->input('is_public_profile', 0);
                $account->save();

                // Sync spoken languages
                $spokenLanguages = $request->input('spoken_languages', []);

                // Ensure we have a proper array and filter out invalid values
                if (is_array($spokenLanguages)) {
                    $spokenLanguages = array_filter($spokenLanguages, function($value) {
                        return !empty($value) && is_numeric($value) && $value > 0;
                    });
                    // Re-index the array to avoid issues with sync
                    $spokenLanguages = array_values($spokenLanguages);
                } else {
                    $spokenLanguages = [];
                }

                $account->spokenLanguages()->sync($spokenLanguages);
            });

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('account.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(Account $account)
    {
        return DeleteResourceAction::make($account);
    }

    public function getList(Request $request)
    {
        $keyword = BaseHelper::stringify($request->input('q'));

        if (! $keyword) {
            return $this
                ->httpResponse()
                ->setData([]);
        }

        $data = Account::query()
            ->where('first_name', 'LIKE', '%' . $keyword . '%')
            ->orWhere('last_name', 'LIKE', '%' . $keyword . '%')
            // ->select(['id', 'first_name', 'last_name'])
            ->take(10)
            ->get();

        return $this
            ->httpResponse()
            ->setData(AccountResourceSelect2::collection($data));
    }
    public function getDeveloperList(Request $request)
    {
        $keyword = BaseHelper::stringify($request->input('q'));

        if (! $keyword) {
            return $this
                ->httpResponse()
                ->setData([]);
        }

        $data = Account::query()
            ->where('account_type', AccountTypeEnum::DEVELOPER)
            ->where(function($query) use ($keyword) {
                $query->where('first_name', 'LIKE', '%' . $keyword . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $keyword . '%');
            })
            ->take(10)
            ->get();

        return $this
            ->httpResponse()
            ->setData(AccountResourceSelect2::collection($data));
    }

    public function verifyEmail(int|string $id, Request $request)
    {
        $account = Account::query()
            ->where([
                'id' => $id,
                'confirmed_at' => null,
            ])->firstOrFail();

        $account->confirmed_at = Carbon::now();
        $account->save();

        event(new UpdatedContentEvent(ACCOUNT_MODULE_SCREEN_NAME, $request, $account));

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('account.index'))
            ->withUpdatedSuccessMessage();
    }
}
