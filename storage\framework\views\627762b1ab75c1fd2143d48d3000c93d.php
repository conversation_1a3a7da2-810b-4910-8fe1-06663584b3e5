<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="format-detection" content="telephone=no">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <?php if(theme_option('favicon')): ?>
            <link href="<?php echo e(RvMedia::getImageUrl(theme_option('favicon'))); ?>" rel="shortcut icon">
        <?php endif; ?>

        <title><?php echo e(PageTitle::getTitle(false)); ?></title>

        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <?php echo $__env->make('plugins/real-estate::themes.dashboard.layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <script type="text/javascript">
            window.trans = JSON.parse('<?php echo addslashes(json_encode(trans('plugins/real-estate::dashboard'))); ?>');
            var XmetrVariables = XmetrVariables || {};
            XmetrVariables.languages = {
                tables: <?php echo json_encode(trans('core/base::tables'), JSON_HEX_APOS); ?>,
                notices_msg: <?php echo json_encode(trans('core/base::notices'), JSON_HEX_APOS); ?>,
                pagination: <?php echo json_encode(trans('pagination'), JSON_HEX_APOS); ?>,
                system: {
                    'character_remain': '<?php echo e(trans('core/base::forms.character_remain')); ?>'
                }
            };
            var RV_MEDIA_URL = {
                'media_upload_from_editor': '<?php echo e(route('public.account.upload-from-editor')); ?>'
            };
        </script>

        <?php echo apply_filters('account_dashboard_header', null); ?>

        <?php echo $__env->yieldPushContent('header'); ?>
    </head>

    <body <?php if(BaseHelper::isRtlEnabled()): ?> dir="rtl" <?php endif; ?>>
        <?php echo apply_filters('real_estate_dashboard_header', null); ?>

        <div class="wrapper">
            <?php echo $__env->yieldContent('body', view('plugins/real-estate::themes.dashboard.layouts.body')); ?>
        </div>

        <?php echo $__env->make('plugins/real-estate::themes.dashboard.layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo Assets::renderFooter(); ?>

        <?php echo $__env->yieldPushContent('scripts'); ?>
        <?php echo $__env->yieldPushContent('footer'); ?>
        <?php echo apply_filters('real_estate_dashboard_footer', null); ?>

        <?php echo apply_filters(THEME_FRONT_FOOTER, null); ?>


        
    </body>
</html>
<?php /**PATH D:\laragon\www\xmetr\platform/plugins/real-estate/resources/views/themes/dashboard/layouts/master.blade.php ENDPATH**/ ?>