<div class="rental-conditions mb-5">
    <h4 class="title"><?php echo e(__('Rental Conditions')); ?></h4>
    <div class="flex items-center gap-[5px] flex-wrap">

        <?php if($property->furnished): ?>
          <div class="inline-flex items-center gap-2 px-6 py-2 bg-white border-2 border-gray-200 rounded-full border-solid">
            <span class="text-gray-800 font-medium text-lg"><?php echo e(__('Furnished')); ?></span>
        </div>
        <?php endif; ?>
        <?php if($property->pets_allowed): ?>
        <div class="inline-flex items-center gap-2 px-6 py-2 bg-white border-2 border-gray-200 rounded-full border-solid">
            <span class="text-gray-800 font-medium text-lg"><?php echo e(__('Pets Allowed')); ?></span>
        </div>
        <?php endif; ?>
        <?php if($property->smoking_allowed): ?>
        <div class="inline-flex items-center gap-2 px-6 py-2 bg-white border-2 border-gray-200 rounded-full border-solid">
            <span class="text-gray-800 font-medium text-lg"><?php echo e(__('Smoking Allowed')); ?></span>
        </div>
        <?php endif; ?>
        <?php if($property->online_view_tour): ?>
        <div class="inline-flex items-center gap-2 px-6 py-2 bg-white border-2 border-gray-200 rounded-full border-solid">
            <span class="text-gray-800 font-medium text-lg"><?php echo e(__('Online View Tour')); ?></span>
        </div>
        <?php endif; ?>
        <?php if(!empty(trim($property->rental_period))): ?>
        <div class="inline-flex items-center gap-2 px-6 py-2 bg-white border-2 border-gray-200 rounded-full border-solid">
            <span class="text-gray-800 font-medium text-lg"><?php echo e($property->rental_period->label()); ?></span>
        </div>
        <?php endif; ?>

        <?php if($property->required_documents_enum): ?>
        <?php $__currentLoopData = $property->required_documents_enum; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="inline-flex items-center gap-2 px-6 py-2 bg-white border-2 border-gray-200 rounded-full border-solid">
            <span class="text-gray-800 font-medium text-lg"><?php echo e($document->label()); ?></span>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    </div>

</div>
<span class="w-full h-[1px] block bg-[#D6D6D7]"></span>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/single-layouts/partials/rental-conditions.blade.php ENDPATH**/ ?>