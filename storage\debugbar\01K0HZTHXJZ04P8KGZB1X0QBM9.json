{"__meta": {"id": "01K0HZTHXJZ04P8KGZB1X0QBM9", "datetime": "2025-07-19 18:40:58", "utime": **********.291229, "method": "POST", "uri": "/admin/real-estate/accounts/edit/167", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.507799, "end": **********.29125, "duration": 1.****************, "duration_str": "1.78s", "measures": [{"label": "Booting", "start": **********.507799, "relative_start": 0, "end": **********.528894, "relative_end": **********.528894, "duration": 1.****************, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.528913, "relative_start": 1.****************, "end": **********.291253, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "762ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.550732, "relative_start": 1.****************, "end": **********.574805, "relative_end": **********.574805, "duration": 0.024073123931884766, "duration_str": "24.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.286312, "relative_start": 1.****************, "end": **********.288772, "relative_end": **********.288772, "duration": 0.002460002899169922, "duration_str": "2.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 19, "nb_statements": 19, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04238, "accumulated_duration_str": "42.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.5937698, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 1.864}, {"sql": "select * from `re_accounts` where `id` = '167' limit 1", "type": "query", "params": [], "bindings": ["167"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.599294, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.864, "width_percent": 1.958}, {"sql": "select count(*) as aggregate from `re_accounts` where `email` = '<EMAIL>' and `id` <> '167'", "type": "query", "params": [], "bindings": ["<EMAIL>", "167"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.115741, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "xmetr", "explain": null, "start_percent": 3.823, "width_percent": 0.967}, {"sql": "select count(*) as aggregate from `re_spoken_languages` where `id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.117521, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "xmetr", "explain": null, "start_percent": 4.79, "width_percent": 0.708}, {"sql": "select count(*) as aggregate from `re_spoken_languages` where `id` = '6'", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.118724, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "xmetr", "explain": null, "start_percent": 5.498, "width_percent": 1.133}, {"sql": "select count(*) as aggregate from `re_spoken_languages` where `id` = '9'", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.12011, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "xmetr", "explain": null, "start_percent": 6.63, "width_percent": 0.731}, {"sql": "select * from `re_spoken_languages` where `status` = 'published' order by `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/FieldOptions/SpokenLanguageMultiSelectFieldOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\FieldOptions\\SpokenLanguageMultiSelectFieldOption.php", "line": 81}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/FieldOptions/SpokenLanguageMultiSelectFieldOption.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\FieldOptions\\SpokenLanguageMultiSelectFieldOption.php", "line": 23}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountForm.php", "line": 110}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}], "start": **********.140069, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.362, "width_percent": 1.227}, {"sql": "select `re_spoken_languages`.`id` from `re_spoken_languages` inner join `re_account_spoken_languages` on `re_spoken_languages`.`id` = `re_account_spoken_languages`.`spoken_language_id` where `re_account_spoken_languages`.`account_id` = 167", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountForm.php", "line": 112}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.156894, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AccountForm.php:112", "source": {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountForm.php", "line": 112}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FAccountForm.php:112", "ajax": false, "filename": "AccountForm.php", "line": "112"}, "connection": "xmetr", "explain": null, "start_percent": 8.589, "width_percent": 0.944}, {"sql": "update `re_accounts` set `description` = '', `account_type` = 'developer', `company` = '', `re_accounts`.`updated_at` = '2025-07-19 18:40:58' where `id` = 167", "type": "query", "params": [], "bindings": ["", {"value": "developer", "label": "Developer"}, "", "2025-07-19 18:40:58", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 138}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 117}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.188308, "duration": 0.00667, "duration_str": "6.67ms", "memory": 0, "memory_str": null, "filename": "AccountController.php:138", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 138}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FAccountController.php:138", "ajax": false, "filename": "AccountController.php", "line": "138"}, "connection": "xmetr", "explain": null, "start_percent": 9.533, "width_percent": 15.739}, {"sql": "select * from `re_account_spoken_languages` where `re_account_spoken_languages`.`account_id` = 167", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 154}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 117}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.202005, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "AccountController.php:154", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 154}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FAccountController.php:154", "ajax": false, "filename": "AccountController.php", "line": "154"}, "connection": "xmetr", "explain": null, "start_percent": 25.271, "width_percent": 2.147}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'social_links' and `reference_id` = 167 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account') limit 1", "type": "query", "params": [], "bindings": ["social_links", 167, "Xmetr\\RealEstate\\Models\\Account"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 63}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Traits/Forms/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Traits\\Forms\\HasMetadata.php", "line": 71}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 498}], "start": **********.207533, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 27.419, "width_percent": 1.699}, {"sql": "insert into `meta_boxes` (`meta_key`, `reference_id`, `reference_type`, `meta_value`, `updated_at`, `created_at`) values ('social_links', 167, 'Xmetr\\\\RealEstate\\\\Models\\\\Account', '[\\\"[]\\\"]', '2025-07-19 18:40:58', '2025-07-19 18:40:58')", "type": "query", "params": [], "bindings": ["social_links", 167, "Xmetr\\RealEstate\\Models\\Account", "[\"[]\"]", "2025-07-19 18:40:58", "2025-07-19 18:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 63}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Traits/Forms/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Traits\\Forms\\HasMetadata.php", "line": 71}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 498}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 117}], "start": **********.212766, "duration": 0.006809999999999999, "duration_str": "6.81ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:156", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:156", "ajax": false, "filename": "MetaBox.php", "line": "156"}, "connection": "xmetr", "explain": null, "start_percent": 29.118, "width_percent": 16.069}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'telegram' and `reference_id` = 167 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account') limit 1", "type": "query", "params": [], "bindings": ["telegram", 167, "Xmetr\\RealEstate\\Models\\Account"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 63}, {"index": 21, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 890}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.222094, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 45.186, "width_percent": 1.723}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'whatsapp' and `reference_id` = 167 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account') limit 1", "type": "query", "params": [], "bindings": ["whatsapp", 167, "Xmetr\\RealEstate\\Models\\Account"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 63}, {"index": 21, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 891}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.226557, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 46.909, "width_percent": 1.793}, {"sql": "insert into `meta_boxes` (`meta_key`, `reference_id`, `reference_type`, `meta_value`, `updated_at`, `created_at`) values ('whatsapp', 167, 'Xmetr\\\\RealEstate\\\\Models\\\\Account', '[\\\"<EMAIL>\\\"]', '2025-07-19 18:40:58', '2025-07-19 18:40:58')", "type": "query", "params": [], "bindings": ["whatsapp", 167, "Xmetr\\RealEstate\\Models\\Account", "[\"<EMAIL>\"]", "2025-07-19 18:40:58", "2025-07-19 18:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 63}, {"index": 18, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 891}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}], "start": **********.230103, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:156", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:156", "ajax": false, "filename": "MetaBox.php", "line": "156"}, "connection": "xmetr", "explain": null, "start_percent": 48.702, "width_percent": 10.807}, {"sql": "select * from `slugs` where (`reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Account' and `reference_id` = 167) limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\RealEstate\\Models\\Account", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": **********.24176, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 59.509, "width_percent": 2.029}, {"sql": "insert into `slugs` (`key`, `reference_type`, `reference_id`, `prefix`, `updated_at`, `created_at`) values ('Kate', 'Xmetr\\\\RealEstate\\\\Models\\\\Account', 167, 'agents', '2025-07-19 18:40:58', '2025-07-19 18:40:58')", "type": "query", "params": [], "bindings": ["<PERSON>", "Xmetr\\RealEstate\\Models\\Account", 167, "agents", "2025-07-19 18:40:58", "2025-07-19 18:40:58"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 26, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/AccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\AccountController.php", "line": 117}], "start": **********.24787, "duration": 0.00754, "duration_str": "7.54ms", "memory": 0, "memory_str": null, "filename": "UpdatedContentListener.php:62", "source": {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 62}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FListeners%2FUpdatedContentListener.php:62", "ajax": false, "filename": "UpdatedContentListener.php", "line": "62"}, "connection": "xmetr", "explain": null, "start_percent": 61.538, "width_percent": 17.791}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 889}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Listeners\\UpdatedContentListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.2587821, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 79.33, "width_percent": 1.463}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'account', 'updated', 1, 1, 167, 'Kate ', 'primary', '2025-07-19 18:40:58', '2025-07-19 18:40:58', '{\\\"first_name\\\":\\\"Kate\\\",\\\"model\\\":\\\"Xmetr\\\\\\\\RealEstate\\\\\\\\Models\\\\\\\\Account\\\",\\\"slug\\\":null,\\\"slug_id\\\":\\\"0\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"company\\\":null,\\\"description\\\":null,\\\"phone\\\":\\\"+************\\\",\\\"dob\\\":\\\"2025-06-18\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"telegram\\\":\\\"@nikiforova_k\\\",\\\"whatsapp\\\":\\\"<EMAIL>\\\",\\\"country_id\\\":\\\"28\\\",\\\"state_id\\\":null,\\\"city_id\\\":null,\\\"district_id\\\":null,\\\"account_type\\\":\\\"developer\\\",\\\"spoken_languages\\\":[\\\"1\\\",\\\"6\\\",\\\"9\\\"],\\\"is_change_password\\\":\\\"0\\\",\\\"social_links\\\":\\\"[]\\\",\\\"language\\\":\\\"en_US\\\",\\\"avatar_image\\\":null,\\\"is_featured\\\":\\\"0\\\",\\\"is_verified\\\":\\\"0\\\",\\\"is_public_profile\\\":\\\"0\\\",\\\"submitter\\\":\\\"apply\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "account", "updated", 1, 1, 167, "<PERSON> ", "primary", "2025-07-19 18:40:58", "2025-07-19 18:40:58", "{\"first_name\":\"<PERSON>\",\"model\":\"Xmetr\\\\RealEstate\\\\Models\\\\Account\",\"slug\":null,\"slug_id\":\"0\",\"is_slug_editable\":\"1\",\"company\":null,\"description\":null,\"phone\":\"+************\",\"dob\":\"2025-06-18\",\"email\":\"<EMAIL>\",\"telegram\":\"@nikiforova_k\",\"whatsapp\":\"<EMAIL>\",\"country_id\":\"28\",\"state_id\":null,\"city_id\":null,\"district_id\":null,\"account_type\":\"developer\",\"spoken_languages\":[\"1\",\"6\",\"9\"],\"is_change_password\":\"0\",\"social_links\":\"[]\",\"language\":\"en_US\",\"avatar_image\":null,\"is_featured\":\"0\",\"is_verified\":\"0\",\"is_public_profile\":\"0\",\"submitter\":\"apply\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 514}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": **********.274994, "duration": 0.008140000000000001, "duration_str": "8.14ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 80.793, "width_percent": 19.207}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\SpokenLanguage": {"value": 9, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FSpokenLanguage.php:1", "ajax": false, "filename": "SpokenLanguage.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/accounts/edit/167", "action_name": "account.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\AccountController@update", "uri": "POST admin/real-estate/accounts/edit/{account}", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\AccountController@update<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FAccountController.php:112\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers", "prefix": "admin/real-estate/accounts", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FAccountController.php:112\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/AccountController.php:112-161</a>", "middleware": "web, core, auth", "duration": "1.78s", "peak_memory": "46MB", "response": "Redirect to https://xmetr.gc/admin/real-estate/accounts/edit/167", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Kate</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"13 characters\">+************</span>\"\n  \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-18</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>telegram</span>\" => \"<span class=sf-dump-str title=\"13 characters\">@nikiforova_k</span>\"\n  \"<span class=sf-dump-key>whatsapp</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n  \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>account_type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">developer</span>\"\n  \"<span class=sf-dump-key>spoken_languages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>6</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>is_change_password</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>password_confirmation</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>social_links</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n  \"<span class=sf-dump-key>avatar_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_verified</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_public_profile</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-185824060 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">614</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://xmetr.gc/admin/real-estate/accounts/edit/167</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1708 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; project_wishlist=25; wishlist=657; _hjSession_6417422=eyJpZCI6Ijg3Mjk5NzNiLTU1NjUtNDEyYi1hNTZlLTJlMTk4ODQzYTk2MCIsImMiOjE3NTI5NDgxNjUzNzUsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1752948165$o95$g1$t1752949860$j58$l0$h0; XSRF-TOKEN=eyJpdiI6IkVBMFRZV0FrU2ZYYi9xQWJkVExBRlE9PSIsInZhbHVlIjoiQnB3d09XSVNnRU1ka3FpYmZGaGd3VlZrTkVpZ1ZhRkJ5RHJjYVVUcHlXTDdjL2lqTHZJSUNVSGNmQS9KUTAyT1J2NkRmK2lWMkU3K3dWaXVnclNpSGFpR3Z0cjYxZUQvcWhmVU4xZWZ3cEtzYURWQ08yQTNmeEsvWG9QRnpSUzEiLCJtYWMiOiIyZmEzMWZhZmI4ZGU0Mjk5MDNjZjczOTUzYjFiMGNkMjljNTE1OTM0MjUyZTU2OGIxMGQxZTZmMDAwZjU5YTMzIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IlgybnZUczFLL0Nxd09DNFRTUWUzTWc9PSIsInZhbHVlIjoicTV0T3Q2OWs4T0R6azNtdHpsRU5oR0tHTndzczFCODJvMGVteVpoTTZmTjYxV0UzMUNFSE9TR1A4RlIvUU5VcGJqMnpBa3Z3L3M5R2pwcmgxNmJHdmNXUllDcFdtUnByc0xISW1sUWVRZEtDZW1ieHM2cFFNSVFLR1J0VXVpazEiLCJtYWMiOiJkYjdhNGNmZjI4NGRmZGE3M2FmODZiYjgxNGQxZDM0Zjc1ZTU1ZmVlYmYwYTJkODk2NTMwNWMzNGFhZjYyOTMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185824060\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1595375236 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RobK1zu3t1m8xEzREPlVbKwr1yqlht8JLepj3TJp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595375236\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 19 Jul 2025 18:40:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://xmetr.gc/admin/real-estate/accounts/edit/167</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5GTuhdoIPcY7AWDKnUSnZpVgwiM8AnXCOFbZcFZd</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">https://xmetr.gc/admin/real-estate/accounts/edit/167</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/real-estate/accounts/edit/167", "action_name": "account.edit.update", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\AccountController@update"}, "badge": "302 Found"}}