<?php if($reviews->isNotEmpty()): ?>
    <ul class="box-review">
        <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="list-review-item">
                <div class="avatar avt-60 round">
                    <?php echo e(RvMedia::image($review->author->avatar_url, $review->author->name)); ?>

                </div>
                <div class="content w-100">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="name h7 fw-7 text-black">
                            <?php echo e($review->author->name); ?>

                        </div>
                        <span class="mt-4 d-inline-block date body-3 text-variant-2">
                    <?php echo e(Theme::formatDate($review->created_at)); ?>

                </span>
                    </div>

                    <?php echo $__env->make(Theme::getThemeNamespace('views.real-estate.partials.star'), ['class' => 'mt-8', 'star' => $review->star], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <p class="mt-12 body-2 text-black">
                        <?php echo BaseHelper::clean(nl2br($review->content)); ?>

                    </p>
                </div>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
<?php else: ?>
    <p class="text-muted text-center h6 mt-3"><?php echo e(__('Looks like there are no reviews!')); ?></p>
<?php endif; ?>

<?php if($reviews->hasPages()): ?>
    <div class="pagination d-flex justify-content-center mt-5">
        <?php echo e($reviews->onEachSide(1)->withQueryString()->links(Theme::getThemeNamespace('partials.pagination'))); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/partials/reviews-list.blade.php ENDPATH**/ ?>